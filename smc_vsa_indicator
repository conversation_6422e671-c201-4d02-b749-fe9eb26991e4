//@version=5
indicator("VSA + SMC Pro Scalping System V2", shorttitle="VSA+SMC Pro", overlay=true, max_boxes_count=200, max_lines_count=200, max_labels_count=100)

// ========================= INPUTS =========================
// VSA Settings
volume_ma_length = input.int(20, "Volume MA Length", minval=1, group="VSA Settings")
volume_threshold = input.float(1.8, "High Volume Threshold", minval=1.0, step=0.1, group="VSA Settings")
volume_low_threshold = input.float(0.7, "Low Volume Threshold", minval=0.1, step=0.1, group="VSA Settings")
spread_threshold = input.float(0.8, "Wide Spread Threshold", minval=0.1, step=0.1, group="VSA Settings")
effort_threshold = input.float(2.0, "Effort Analysis Threshold", minval=1.0, step=0.1, group="VSA Settings")

// SMC Settings
swing_length = input.int(5, "Swing Detection Length", minval=3, maxval=15, group="SMC Settings")
fvg_threshold = input.float(0.1, "FVG Threshold %", minval=0.02, step=0.01, group="SMC Settings")
ob_strength = input.int(3, "Order Block Strength", minval=2, maxval=10, group="SMC Settings")
structure_confirmation = input.int(2, "Structure Break Confirmation", minval=1, maxval=5, group="SMC Settings")

// Multi-Timeframe (Fixed timeframe format)
htf = input.timeframe("60", "Higher Timeframe (minutes)", group="Multi-Timeframe", 
     tooltip="Use minutes: 15, 30, 60, 240 etc.")
use_htf_filter = input.bool(true, "Use HTF Trend Filter", group="Multi-Timeframe")

// Advanced Filters
use_session_filter = input.bool(true, "Filter Trading Sessions", group="Advanced Filters")
london_session = input.session("0800-1600", "London Session", group="Advanced Filters")
newyork_session = input.session("1330-2000", "New York Session", group="Advanced Filters")
min_rr_ratio = input.float(1.5, "Minimum Risk:Reward Ratio", minval=1.0, step=0.1, group="Advanced Filters")

// Display Settings
show_vsa = input.bool(true, "Show VSA Patterns", group="Display")
show_structure = input.bool(true, "Show Market Structure", group="Display")
show_fvg = input.bool(true, "Show Fair Value Gaps", group="Display")
show_ob = input.bool(true, "Show Order Blocks", group="Display")
show_liquidity = input.bool(true, "Show Liquidity Levels", group="Display")
show_confluence_only = input.bool(false, "Show Confluence Signals Only", group="Display")

// ========================= UTILITY FUNCTIONS =========================
// ATR for dynamic thresholds
atr = ta.atr(14)
atr_multiplier = 1.5

// Session filter
in_london = not use_session_filter or time(timeframe.period, london_session)
in_newyork = not use_session_filter or time(timeframe.period, newyork_session)
in_session = in_london or in_newyork

// ========================= ENHANCED VSA FUNCTIONS =========================
// Advanced Volume Analysis
volume_ma = ta.sma(volume, volume_ma_length)
volume_ema = ta.ema(volume, volume_ma_length)
volume_ratio = volume / volume_ma
volume_ema_ratio = volume / volume_ema

high_volume = volume_ratio >= volume_threshold
very_high_volume = volume_ratio >= (volume_threshold * 1.5)
low_volume = volume_ratio <= volume_low_threshold

// Enhanced Spread Analysis
spread = high - low
spread_ma = ta.sma(spread, volume_ma_length)
spread_ratio = spread / spread_ma
spread_atr_ratio = spread / atr

wide_spread = spread_ratio >= spread_threshold or spread_atr_ratio >= 1.2
narrow_spread = spread_ratio <= (1/spread_threshold) and spread_atr_ratio <= 0.6
ultra_wide_spread = spread_ratio >= (spread_threshold * 1.5)

// Refined Close Position Analysis
close_position = (high - low) > 0 ? (close - low) / (high - low) : 0.5
close_very_high = close_position >= 0.8
close_high = close_position >= 0.7 and close_position < 0.8
close_mid = close_position > 0.3 and close_position < 0.7
close_low = close_position > 0.2 and close_position <= 0.3
close_very_low = close_position <= 0.2

// Enhanced VSA Pattern Detection with Context
// Bullish Patterns
no_supply = high_volume and narrow_spread and close_high and close > open and volume[1] < volume
test_supply = high_volume and narrow_spread and close_high and close > open and high <= high[1]
spring = very_high_volume and close > open and close_position > 0.7 and low < low[1] and close > close[1]
bag_holding = low_volume and wide_spread and close_low and close < open

// Bearish Patterns  
no_demand = high_volume and narrow_spread and close_low and close < open and volume[1] < volume
test_demand = high_volume and narrow_spread and close_low and close < open and low >= low[1]
up_thrust = very_high_volume and close < open and close_position < 0.3 and high > high[1] and close < close[1]
selling_climax = very_high_volume and ultra_wide_spread and close_very_low and close < open

// Effort vs Result Analysis
effort_up = high_volume and wide_spread and close > open
result_up = close > close[1] and close > close[2]
effort_down = high_volume and wide_spread and close < open  
result_down = close < close[1] and close < close[2]

no_result_up = effort_up and not result_up // Bearish divergence
no_result_down = effort_down and not result_down // Bullish divergence

// ========================= ADVANCED SMC FUNCTIONS =========================
// Enhanced Swing Point Detection with Strength
swing_high_strength = 0
swing_low_strength = 0

// Calculate swing strength
for i = 1 to swing_length
    if high > high[i] and high > high[-i]
        swing_high_strength += 1
    if low < low[i] and low < low[-i]
        swing_low_strength += 1

is_swing_high = ta.pivothigh(high, swing_length, swing_length) and swing_high_strength >= swing_length/2
is_swing_low = ta.pivotlow(low, swing_length, swing_length) and swing_low_strength >= swing_length/2

// Dynamic arrays for swing points
var swing_highs = array.new<float>()
var swing_high_bars = array.new<int>()
var swing_lows = array.new<float>()
var swing_low_bars = array.new<int>()

// Update swing arrays
if not na(is_swing_high)
    array.push(swing_highs, is_swing_high)
    array.push(swing_high_bars, bar_index - swing_length)
    if array.size(swing_highs) > 20
        array.shift(swing_highs)
        array.shift(swing_high_bars)

if not na(is_swing_low)
    array.push(swing_lows, is_swing_low)
    array.push(swing_low_bars, bar_index - swing_length)
    if array.size(swing_lows) > 20
        array.shift(swing_lows)
        array.shift(swing_low_bars)

// Enhanced Break of Structure with Confirmation
bos_bull = false
bos_bear = false
choch_bull = false  
choch_bear = false
liquidity_grab_bull = false
liquidity_grab_bear = false

if array.size(swing_highs) >= 3 and array.size(swing_lows) >= 3
    last_high = array.get(swing_highs, array.size(swing_highs) - 1)
    prev_high = array.get(swing_highs, array.size(swing_highs) - 2)
    last_low = array.get(swing_lows, array.size(swing_lows) - 1)
    prev_low = array.get(swing_lows, array.size(swing_lows) - 2)
    
    // BOS Detection with confirmation
    if close > last_high and high > last_high
        if last_high > prev_high and last_low > prev_low
            bos_bull := true
        else if last_high < prev_high
            choch_bull := true
    
    if close < last_low and low < last_low
        if last_low < prev_low and last_high < prev_high
            bos_bear := true
        else if last_low > prev_low
            choch_bear := true
    
    // Liquidity Grab Detection
    if high > last_high and close < last_high
        liquidity_grab_bear := true
    if low < last_low and close > last_low
        liquidity_grab_bull := true

// Enhanced Fair Value Gap Detection
is_fvg_bull = false
is_fvg_bear = false
fvg_bull_top = 0.0
fvg_bull_bottom = 0.0
fvg_bear_top = 0.0
fvg_bear_bottom = 0.0

// Bullish FVG: Previous high < Current low
if low > high[2] 
    gap_size = (low - high[2]) / high[2] * 100
    if gap_size >= fvg_threshold and volume[1] > volume_ma[1]
        is_fvg_bull := true
        fvg_bull_top := low
        fvg_bull_bottom := high[2]

// Bearish FVG: Previous low > Current high  
if high < low[2]
    gap_size = (low[2] - high) / low[2] * 100
    if gap_size >= fvg_threshold and volume[1] > volume_ma[1]
        is_fvg_bear := true
        fvg_bear_top := low[2]
        fvg_bear_bottom := high

// Enhanced Order Block Detection
var ob_bull_boxes = array.new<box>()
var ob_bear_boxes = array.new<box>()

// Bullish Order Block
strong_move_up = false
ob_bull_detected = false

for i = 1 to 10
    move_size = (high[i] - low[i+5]) / low[i+5] * 100
    move_volume = volume[i] / volume_ma[i]
    if move_size >= 2.0 and move_volume >= 1.5 and close[i] > open[i]
        strong_move_up := true
        // Find the last bearish candle before the move
        for j = i+1 to i+ob_strength
            if close[j] < open[j] and high[j] < high[i]
                if array.size(ob_bull_boxes) < 50
                    box_id = box.new(bar_index[j+1], low[j], bar_index, high[j], 
                                   border_color=color.new(color.blue, 70), 
                                   bgcolor=color.new(color.blue, 85))
                    array.push(ob_bull_boxes, box_id)
                ob_bull_detected := true
                break
        break

// Bearish Order Block  
strong_move_down = false
ob_bear_detected = false

for i = 1 to 10
    move_size = (high[i+5] - low[i]) / high[i+5] * 100
    move_volume = volume[i] / volume_ma[i]
    if move_size >= 2.0 and move_volume >= 1.5 and close[i] < open[i]
        strong_move_down := true
        // Find the last bullish candle before the move
        for j = i+1 to i+ob_strength  
            if close[j] > open[j] and low[j] > low[i]
                if array.size(ob_bear_boxes) < 50
                    box_id = box.new(bar_index[j+1], low[j], bar_index, high[j],
                                   border_color=color.new(color.red, 70),
                                   bgcolor=color.new(color.red, 85))
                    array.push(ob_bear_boxes, box_id)
                ob_bear_detected := true
                break
        break

// ========================= HIGHER TIMEFRAME ANALYSIS (FIXED) =========================
// Request higher timeframe data with proper syntax
htf_close = request.security(syminfo.tickerid, htf, close, lookahead=barmerge.lookahead_off)
htf_high = request.security(syminfo.tickerid, htf, high, lookahead=barmerge.lookahead_off)
htf_low = request.security(syminfo.tickerid, htf, low, lookahead=barmerge.lookahead_off)
htf_volume = request.security(syminfo.tickerid, htf, volume, lookahead=barmerge.lookahead_off)

htf_sma_fast = request.security(syminfo.tickerid, htf, ta.sma(close, 10), lookahead=barmerge.lookahead_off)
htf_sma_slow = request.security(syminfo.tickerid, htf, ta.sma(close, 21), lookahead=barmerge.lookahead_off)
htf_ema = request.security(syminfo.tickerid, htf, ta.ema(close, 50), lookahead=barmerge.lookahead_off)

// HTF Trend Analysis
htf_strong_bull = htf_close > htf_sma_fast and htf_sma_fast > htf_sma_slow and htf_close > htf_ema
htf_strong_bear = htf_close < htf_sma_fast and htf_sma_fast < htf_sma_slow and htf_close < htf_ema
htf_bull_bias = htf_close > htf_sma_fast or htf_close > htf_ema
htf_bear_bias = htf_close < htf_sma_fast or htf_close < htf_ema

// HTF momentum
htf_momentum_bull = htf_close > htf_close[1] and htf_volume > ta.sma(htf_volume, 10)
htf_momentum_bear = htf_close < htf_close[1] and htf_volume > ta.sma(htf_volume, 10)

// ========================= ENHANCED SIGNAL GENERATION =========================
// VSA Signal Strength
vsa_bull_strength = 0
vsa_bear_strength = 0

if no_supply
    vsa_bull_strength += 3
if spring  
    vsa_bull_strength += 4
if test_supply
    vsa_bull_strength += 2
if no_result_down
    vsa_bull_strength += 2

if no_demand
    vsa_bear_strength += 3
if up_thrust
    vsa_bear_strength += 4  
if test_demand
    vsa_bear_strength += 2
if no_result_up
    vsa_bear_strength += 2

// SMC Signal Strength  
smc_bull_strength = 0
smc_bear_strength = 0

if bos_bull
    smc_bull_strength += 4
if choch_bull
    smc_bull_strength += 3
if is_fvg_bull
    smc_bull_strength += 2
if ob_bull_detected
    smc_bull_strength += 3
if liquidity_grab_bull
    smc_bull_strength += 2

if bos_bear
    smc_bear_strength += 4
if choch_bear  
    smc_bear_strength += 3
if is_fvg_bear
    smc_bear_strength += 2
if ob_bear_detected
    smc_bear_strength += 3
if liquidity_grab_bear
    smc_bear_strength += 2

// Confluence Requirements
min_vsa_strength = 3
min_smc_strength = 3
min_total_strength = 7

// Enhanced Buy Signal
vsa_buy_signal = vsa_bull_strength >= min_vsa_strength
smc_buy_signal = smc_bull_strength >= min_smc_strength
volume_buy_confirm = high_volume and volume > volume[1]
htf_buy_confirm = not use_htf_filter or htf_bull_bias
session_buy_confirm = in_session

buy_confluence = vsa_buy_signal and smc_buy_signal and volume_buy_confirm and 
                htf_buy_confirm and session_buy_confirm and 
                (vsa_bull_strength + smc_bull_strength) >= min_total_strength

// Enhanced Sell Signal
vsa_sell_signal = vsa_bear_strength >= min_vsa_strength  
smc_sell_signal = smc_bear_strength >= min_smc_strength
volume_sell_confirm = high_volume and volume > volume[1]
htf_sell_confirm = not use_htf_filter or htf_bear_bias
session_sell_confirm = in_session

sell_confluence = vsa_sell_signal and smc_sell_signal and volume_sell_confirm and
                 htf_sell_confirm and session_sell_confirm and
                 (vsa_bear_strength + smc_bear_strength) >= min_total_strength

// Strong signals (higher confluence)
strong_buy = buy_confluence and htf_strong_bull and very_high_volume and 
            (vsa_bull_strength + smc_bull_strength) >= 10

strong_sell = sell_confluence and htf_strong_bear and very_high_volume and
             (vsa_bear_strength + smc_bear_strength) >= 10

// ========================= RISK MANAGEMENT =========================
// Dynamic stop loss and take profit levels
var float buy_stop_loss = na
var float buy_take_profit = na  
var float sell_stop_loss = na
var float sell_take_profit = na

if buy_confluence
    // Stop loss below recent swing low or order block
    recent_low = array.size(swing_lows) > 0 ? array.get(swing_lows, array.size(swing_lows)-1) : low[5]
    buy_stop_loss := math.min(recent_low, low - atr)
    risk = close - buy_stop_loss
    buy_take_profit := close + (risk * min_rr_ratio)

if sell_confluence  
    // Stop loss above recent swing high or order block
    recent_high = array.size(swing_highs) > 0 ? array.get(swing_highs, array.size(swing_highs)-1) : high[5]
    sell_stop_loss := math.max(recent_high, high + atr)
    risk = sell_stop_loss - close
    sell_take_profit := close - (risk * min_rr_ratio)

// ========================= PLOTTING =========================
// VSA Patterns (only if not confluence-only mode)
plotshape(show_vsa and not show_confluence_only and no_supply, title="No Supply", 
          location=location.belowbar, style=shape.triangleup, color=color.new(color.green, 30), size=size.tiny)
plotshape(show_vsa and not show_confluence_only and spring, title="Spring", 
          location=location.belowbar, style=shape.diamond, color=color.new(color.lime, 0), size=size.small)
plotshape(show_vsa and not show_confluence_only and no_demand, title="No Demand", 
          location=location.abovebar, style=shape.triangledown, color=color.new(color.red, 30), size=size.tiny)
plotshape(show_vsa and not show_confluence_only and up_thrust, title="Up Thrust", 
          location=location.abovebar, style=shape.diamond, color=color.new(color.orange, 0), size=size.small)

// Structure breaks
plotchar(show_structure and not show_confluence_only and bos_bull, title="BOS↑", 
         location=location.belowbar, char="⬆", color=color.blue, size=size.small)
plotchar(show_structure and not show_confluence_only and bos_bear, title="BOS↓", 
         location=location.abovebar, char="⬇", color=color.purple, size=size.small)
plotchar(show_structure and not show_confluence_only and choch_bull, title="CHoCH↑", 
         location=location.belowbar, char="↗", color=color.aqua, size=size.small)
plotchar(show_structure and not show_confluence_only and choch_bear, title="CHoCH↓", 
         location=location.abovebar, char="↙", color=color.fuchsia, size=size.small)

// Liquidity grabs
plotchar(show_liquidity and liquidity_grab_bull, title="LiqGrab↑", 
         location=location.belowbar, char="⤴", color=color.yellow, size=size.tiny)
plotchar(show_liquidity and liquidity_grab_bear, title="LiqGrab↓", 
         location=location.abovebar, char="⤵", color=color.yellow, size=size.tiny)

// Fair Value Gaps
bgcolor(show_fvg and is_fvg_bull ? color.new(color.green, 90) : na, title="Bull FVG")
bgcolor(show_fvg and is_fvg_bear ? color.new(color.red, 90) : na, title="Bear FVG")

// Main confluence signals
plotshape(buy_confluence, title="BUY CONFLUENCE", location=location.belowbar, 
          style=shape.labelup, color=color.new(color.green, 0), textcolor=color.white, 
          text="BUY", size=size.large)
plotshape(sell_confluence, title="SELL CONFLUENCE", location=location.abovebar, 
          style=shape.labeldown, color=color.new(color.red, 0), textcolor=color.white, 
          text="SELL", size=size.large)

// Strong signals
plotshape(strong_buy, title="STRONG BUY", location=location.belowbar, 
          style=shape.labelup, color=color.new(color.lime, 0), textcolor=color.black, 
          text="🔥BUY", size=size.large)
plotshape(strong_sell, title="STRONG SELL", location=location.abovebar, 
          style=shape.labeldown, color=color.new(color.orange, 0), textcolor=color.black, 
          text="🔥SELL", size=size.large)

// Stop loss and take profit levels
plot(buy_confluence ? buy_stop_loss : na, title="Buy SL", color=color.red, linewidth=1, style=plot.style_linebr)
plot(buy_confluence ? buy_take_profit : na, title="Buy TP", color=color.green, linewidth=1, style=plot.style_linebr)
plot(sell_confluence ? sell_stop_loss : na, title="Sell SL", color=color.red, linewidth=1, style=plot.style_linebr) 
plot(sell_confluence ? sell_take_profit : na, title="Sell TP", color=color.green, linewidth=1, style=plot.style_linebr)

// Enhanced volume bars
barcolor(very_high_volume ? color.yellow : high_volume ? (close > open ? color.lime : color.red) : color.gray, title="Volume Bars")

// ========================= ENHANCED SIGNAL TABLE =========================
var table info_table = table.new(position.top_right, 3, 8, bgcolor=color.new(color.white, 20), border_width=1)

if barstate.islast
    // Headers
    table.cell(info_table, 0, 0, "Component", text_color=color.white, bgcolor=color.new(color.gray, 30), text_size=size.small)
    table.cell(info_table, 1, 0, "Bull", text_color=color.white, bgcolor=color.new(color.gray, 30), text_size=size.small)
    table.cell(info_table, 2, 0, "Bear", text_color=color.white, bgcolor=color.new(color.gray, 30), text_size=size.small)
    
    // VSA Strength
    table.cell(info_table, 0, 1, "VSA Strength", text_color=color.white, text_size=size.small)
    table.cell(info_table, 1, 1, str.tostring(vsa_bull_strength), 
               text_color=vsa_bull_strength >= min_vsa_strength ? color.lime : color.white, text_size=size.small)
    table.cell(info_table, 2, 1, str.tostring(vsa_bear_strength), 
               text_color=vsa_bear_strength >= min_vsa_strength ? color.red : color.white, text_size=size.small)
    
    // SMC Strength  
    table.cell(info_table, 0, 2, "SMC Strength", text_color=color.white, text_size=size.small)
    table.cell(info_table, 1, 2, str.tostring(smc_bull_strength), 
               text_color=smc_bull_strength >= min_smc_strength ? color.lime : color.white, text_size=size.small)
    table.cell(info_table, 2, 2, str.tostring(smc_bear_strength), 
               text_color=smc_bear_strength >= min_smc_strength ? color.red : color.white, text_size=size.small)
    
    // Volume
    table.cell(info_table, 0, 3, "Volume", text_color=color.white, text_size=size.small)
    vol_status = very_high_volume ? "Very High" : high_volume ? "High" : low_volume ? "Low" : "Normal"
    vol_color = very_high_volume ? color.yellow : high_volume ? color.lime : color.white
    table.cell(info_table, 1, 3, vol_status, text_color=vol_color, text_size=size.small)
    table.cell(info_table, 2, 3, vol_status, text_color=vol_color, text_size=size.small)
    
    // HTF Bias
    table.cell(info_table, 0, 4, "HTF Trend", text_color=color.white, text_size=size.small)
    htf_bull_text = htf_strong_bull ? "Strong↑" : htf_bull_bias ? "Bull↑" : "Neutral"
    htf_bear_text = htf_strong_bear ? "Strong↓" : htf_bear_bias ? "Bear↓" : "Neutral"
    table.cell(info_table, 1, 4, htf_bull_text, 
               text_color=htf_strong_bull ? color.lime : htf_bull_bias ? color.green : color.white, text_size=size.small)
    table.cell(info_table, 2, 4, htf_bear_text, 
               text_color=htf_strong_bear ? color.red : htf_bear_bias ? color.orange : color.white, text_size=size.small)
    
    // Session
    table.cell(info_table, 0, 5, "Session", text_color=color.white, text_size=size.small)
    session_text = in_london and in_newyork ? "Overlap" : in_london ? "London" : in_newyork ? "NY" : "Closed"
    session_color = (in_london and in_newyork) ? color.yellow : in_session ? color.aqua : color.gray
    table.cell(info_table, 1, 5, session_text, text_color=session_color, text_size=size.small)
    table.cell(info_table, 2, 5, session_text, text_color=session_color, text_size=size.small)
    
    // Total Strength
    table.cell(info_table, 0, 6, "Total Score", text_color=color.white, text_size=size.small)
    bull_total = vsa_bull_strength + smc_bull_strength
    bear_total = vsa_bear_strength + smc_bear_strength
    table.cell(info_table, 1, 6, str.tostring(bull_total), 
               text_color=bull_total >= min_total_strength ? color.lime : color.white, text_size=size.small)
    table.cell(info_table, 2, 6, str.tostring(bear_total), 
               text_color=bear_total >= min_total_strength ? color.red : color.white, text_size=size.small)
    
    // Final Signal
    table.cell(info_table, 0, 7, "SIGNAL", text_color=color.white, bgcolor=color.new(color.black, 50), text_size=size.normal)
    buy_signal_text = strong_buy ? "🔥BUY" : buy_confluence ? "BUY" : "WAIT"
    sell_signal_text = strong_sell ? "🔥SELL" : sell_confluence ? "SELL" : "WAIT"
    buy_signal_color = strong_buy ? color.lime : buy_confluence ? color.green : color.gray
    sell_signal_color = strong_sell ? color.orange : sell_confluence ? color.red : color.gray
    
    table.cell(info_table, 1, 7, buy_signal_text, text_color=buy_signal_color, 
               bgcolor=color.new(color.black, 50), text_size=size.normal)
    table.cell(info_table, 2, 7, sell_signal_text, text_color=sell_signal_color, 
               bgcolor=color.new(color.black, 50), text_size=size.normal)

// ========================= ALERTS =========================
alertcondition(buy_confluence, title="VSA+SMC Buy Signal", 
               message="🟢 BUY SIGNAL: VSA Strength={{plot('vsa_bull_strength')}} | SMC Strength={{plot('smc_bull_strength')}} | SL: {{plot('buy_stop_loss')}} | TP: {{plot('buy_take_profit')}}")

alertcondition(sell_confluence, title="VSA+SMC Sell Signal", 
               message="🔴 SELL SIGNAL: VSA Strength={{plot('vsa_bear_strength')}} | SMC Strength={{plot('smc_bear_strength')}} | SL: {{plot('sell_stop_loss')}} | TP: {{plot('sell_take_profit')}}")

alertcondition(strong_buy, title="VSA+SMC STRONG Buy", 
               message="🔥🟢 STRONG BUY: Maximum Confluence Detected! HTF Aligned | Very High Volume")

alertcondition(strong_sell, title="VSA+SMC STRONG Sell", 
               message="🔥🔴 STRONG SELL: Maximum Confluence Detected! HTF Aligned | Very High Volume")

// ========================= ADDITIONAL FEATURES =========================

// Market Regime Detection
var string market_regime = "Consolidation"
regime_periods = 50
price_change = (close - close[regime_periods]) / close[regime_periods] * 100
volume_trend = ta.sma(volume, 20) / ta.sma(volume[20], 20)

if math.abs(price_change) > 5 and volume_trend > 1.2
    market_regime := price_change > 0 ? "Strong Uptrend" : "Strong Downtrend"
else if math.abs(price_change) > 2
    market_regime := price_change > 0 ? "Uptrend" : "Downtrend"
else
    market_regime := "Consolidation"

// Volatility Filter
volatility = ta.atr(14) / close * 100
high_volatility = volatility > ta.percentile_linear_interpolation(volatility, 100, 80)
low_volatility = volatility < ta.percentile_linear_interpolation(volatility, 100, 20)

// Enhanced confluence with volatility and regime filters
enhanced_buy = buy_confluence and market_regime != "Strong Downtrend" and not high_volatility
enhanced_sell = sell_confluence and market_regime != "Strong Uptrend" and not high_volatility

// Plot enhanced signals
plotshape(enhanced_buy and not buy_confluence[1], title="ENHANCED BUY", 
          location=location.belowbar, style=shape.labelup, 
          color=color.new(color.blue, 0), textcolor=color.white, 
          text="✓BUY", size=size.normal)

plotshape(enhanced_sell and not sell_confluence[1], title="ENHANCED SELL", 
          location=location.abovebar, style=shape.labeldown, 
          color=color.new(color.purple, 0), textcolor=color.white, 
          text="✓SELL", size=size.normal)

// ========================= PERFORMANCE TRACKING =========================
var int total_signals = 0
var int winning_signals = 0
var float total_pnl = 0.0
var float entry_price = 0.0
var bool in_trade = false
var string trade_direction = ""

// Track trade entries
if buy_confluence and not in_trade
    entry_price := close
    in_trade := true
    trade_direction := "BUY"
    total_signals += 1

if sell_confluence and not in_trade
    entry_price := close  
    in_trade := true
    trade_direction := "SELL"
    total_signals += 1

// Track trade exits (simplified - based on price hitting TP or SL levels)
if in_trade and trade_direction == "BUY"
    if not na(buy_take_profit) and high >= buy_take_profit
        total_pnl += (buy_take_profit - entry_price) / entry_price * 100
        winning_signals += 1
        in_trade := false
        trade_direction := ""
    else if not na(buy_stop_loss) and low <= buy_stop_loss
        total_pnl += (buy_stop_loss - entry_price) / entry_price * 100
        in_trade := false
        trade_direction := ""

if in_trade and trade_direction == "SELL"
    if not na(sell_take_profit) and low <= sell_take_profit
        total_pnl += (entry_price - sell_take_profit) / entry_price * 100
        winning_signals += 1
        in_trade := false
        trade_direction := ""
    else if not na(sell_stop_loss) and high >= sell_stop_loss
        total_pnl += (entry_price - sell_stop_loss) / entry_price * 100
        in_trade := false
        trade_direction := ""

// Calculate win rate
win_rate = total_signals > 0 ? winning_signals / total_signals * 100 : 0

// ========================= EXTENDED INFORMATION TABLE =========================
var table stats_table = table.new(position.bottom_right, 2, 6, bgcolor=color.new(color.black, 80), border_width=1)

if barstate.islast
    // Performance stats
    table.cell(stats_table, 0, 0, "Performance", text_color=color.white, bgcolor=color.new(color.gray, 50), text_size=size.small)
    table.cell(stats_table, 1, 0, "Value", text_color=color.white, bgcolor=color.new(color.gray, 50), text_size=size.small)
    
    table.cell(stats_table, 0, 1, "Total Signals", text_color=color.white, text_size=size.small)
    table.cell(stats_table, 1, 1, str.tostring(total_signals), text_color=color.aqua, text_size=size.small)
    
    table.cell(stats_table, 0, 2, "Win Rate", text_color=color.white, text_size=size.small)
    win_rate_color = win_rate >= 60 ? color.lime : win_rate >= 50 ? color.yellow : color.red
    table.cell(stats_table, 1, 2, str.tostring(win_rate, "#.##") + "%", text_color=win_rate_color, text_size=size.small)
    
    table.cell(stats_table, 0, 3, "Total P&L", text_color=color.white, text_size=size.small)
    pnl_color = total_pnl > 0 ? color.lime : total_pnl < 0 ? color.red : color.white
    table.cell(stats_table, 1, 3, str.tostring(total_pnl, "#.##") + "%", text_color=pnl_color, text_size=size.small)
    
    table.cell(stats_table, 0, 4, "Market Regime", text_color=color.white, text_size=size.small)
    regime_color = market_regime == "Strong Uptrend" or market_regime == "Uptrend" ? color.lime : 
                   market_regime == "Strong Downtrend" or market_regime == "Downtrend" ? color.red : color.yellow
    table.cell(stats_table, 1, 4, market_regime, text_color=regime_color, text_size=size.small)
    
    table.cell(stats_table, 0, 5, "Volatility", text_color=color.white, text_size=size.small)
    vol_status_text = high_volatility ? "High" : low_volatility ? "Low" : "Normal"
    vol_status_color = high_volatility ? color.red : low_volatility ? color.blue : color.white
    table.cell(stats_table, 1, 5, vol_status_text, text_color=vol_status_color, text_size=size.small)

// ========================= COMMENTS AND DOCUMENTATION =========================
// 
// VSA + SMC PRO SCALPING SYSTEM V2 - ENHANCED ACCURACY
//
// MAJOR IMPROVEMENTS:
// 1. Fixed timeframe error - now uses proper minute format (60 instead of "1H")
// 2. Enhanced VSA pattern detection with context and strength scoring
// 3. Advanced SMC structure mapping with confirmation requirements
// 4. Multi-layered confluence system with minimum thresholds
// 5. Dynamic risk management with proper SL/TP calculation
// 6. Session filtering for optimal trading hours
// 7. Market regime and volatility filters
// 8. Real-time performance tracking
// 9. Enhanced order block detection with visual boxes
// 10. Liquidity grab identification for better entries
//
// SIGNAL STRENGTH SYSTEM:
// - VSA patterns scored 2-4 points each based on reliability
// - SMC patterns scored 2-4 points each based on strength  
// - Minimum 3 points required in each category
// - Minimum 7 total points for confluence signal
// - Strong signals require 10+ points + HTF alignment
//
// USAGE INSTRUCTIONS:
// 1. Apply to 5M or 15M charts for scalping
// 2. Set HTF to 60 (1H) or 240 (4H) for trend bias
// 3. Enable session filter for London/NY overlap
// 4. Wait for confluence signals (green/red labels)
// 5. Enter on strong signals (🔥 labels) for highest probability
// 6. Use provided SL/TP levels or adjust based on risk tolerance
// 7. Monitor the signal strength table for real-time analysis
// 8. Track performance in the stats table
//
// RECOMMENDED SETTINGS:
// - Timeframes: 5M/15M for entries, 1H/4H for bias
// - Risk per trade: 1-2% of account
// - Minimum RR ratio: 1:1.5 (adjustable)
// - Sessions: London (08:00-16:00) + New York (13:30-20:00)
// - Avoid trading during major news events
//
// ALERT SETUP:
// - Enable alerts for "VSA+SMC Buy Signal" and "VSA+SMC Sell Signal"
// - Use "STRONG" alerts for highest confidence trades
// - Set up mobile notifications for real-time signals
//
// RISK WARNING:
// - This system provides signals based on technical analysis
// - No system guarantees profits - proper risk management is essential
// - Always use stop losses and position sizing
// - Backtest thoroughly before live trading
// - Results may vary based on market conditions and execution
//