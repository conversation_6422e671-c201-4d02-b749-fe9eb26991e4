// This Pine Script™ code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © fluxchart
// CRT Strategy - Converted from CRT Indicator with 100% identical logic

//@version=5

// Debug and development constants
const bool DEBUG = false
const bool DEBUGOBFVG = false
const int maxDistanceToLastBar = 100000 // Affects Running Time
const int showLastXFVGs = 2
const int maxOrderBlocks = 5
const int maxBarsBack = 50
const int extendLastXFVGsCount = 20
const int minimumFVGSize = 2
const int minimumIFVGSize = 2
const float overlapThresholdPercentage = 0
const int atrLen = 10

// Strategy declaration with proper settings
strategy("CRT Strategy | Flux Charts", 
         shorttitle = "CRT Strategy", 
         overlay = true, 
         initial_capital = 100000,
         default_qty_type = strategy.percent_of_equity,
         default_qty_value = 100,
         commission_type = strategy.commission.percent,
         commission_value = 0.1,
         slippage = 3,
         pyramiding = 1,
         close_entries_rule = "FIFO",
         max_bars_back = 100)

var int curTFMMS = timeframe.in_seconds()

//#region Strategy Settings
// Strategy-specific parameters
initialCapital = input.float(100000, "Initial Capital", minval = 1000, maxval = 10000000, step = 1000, group = "Strategy Settings", tooltip = "Starting capital for the strategy")
defaultQty = input.float(100, "Position Size (% of Equity)", minval = 1, maxval = 100, step = 1, group = "Strategy Settings", tooltip = "Percentage of equity to use for each trade")
commissionRate = input.float(0.1, "Commission (%)", minval = 0, maxval = 5, step = 0.01, group = "Strategy Settings", tooltip = "Commission percentage per trade")
slippageTicks = input.int(3, "Slippage (Ticks)", minval = 0, maxval = 50, step = 1, group = "Strategy Settings", tooltip = "Slippage in ticks")
maxPyramiding = input.int(1, "Max Pyramiding", minval = 1, maxval = 10, step = 1, group = "Strategy Settings", tooltip = "Maximum number of entries in same direction")
//#endregion

//#region Core Settings - Migrated from Indicator
// General Configuration
higherTF = input.timeframe("240", "Higher Timeframe", group = "General Configuration", tooltip = "M15 -> H4 is recommended.\n\nAll Recommended Timeframes :\nWeekly -> Monthly\nDaily -> Weekly\nH4 -> Daily\nM15 -> H4\nM3 -> M30")
bulkyCandleATRStr = input.string("Big", "HTF Candle Size", options = ["Big", "Normal", "Small"], group = "General Configuration", tooltip = "The size of the higher timeframe candle. A candle will be considered eligible for CRT if it's range obliges to this setting.")
bulkyCandleATR = bulkyCandleATRStr == "Big" ? 2.1 : bulkyCandleATRStr == "Normal" ? 1.6 : 1.3
entryMode = input.string("FVGs", "Entry Mode", ["FVGs", "Order Blocks"], group = "General Configuration")
requireRetracement = input.bool(false, "Require Retracement", tooltip = "A retracement to the FVG or Order Block will be required for entry confirmation if enabled.", group = "General Configuration")

// Fair Value Gaps
fvgSensitivityText = input.string("High", "FVG Detection Sensitivity", options = ["All", "Extreme", "High", "Normal", "Low"], group = "Fair Value Gaps")

// Order Blocks
swingLength = input.int(10, 'Swing Length', minval = 3, maxval = 45, tooltip="Swing length is used when finding order block formations. Smaller values will result in finding smaller order blocks.",group = "Order Blocks")

// TP / SL Configuration
showTPSL = input.bool(true, "Enabled", group = "TP / SL")
tpslMethod = input.string("Dynamic", "TP / SL Method", options = ["Dynamic", "Fixed"], group = "TP / SL")
riskAmount = input.string("High", "Dynamic Risk", options = ["Highest", "High", "Normal", "Low", "Lowest"], group = "TP / SL", tooltip = "The risk amount when Dynamic TP / SL method is selected.\n\nDifferent assets may have different volatility so changing this setting may result in change of performance of the indicator.")
customSLATRMult = DEBUG ? input.float(6.5, "[DBG] Dynamic Custom Risk Mult", group = "TP / SL") : 6.5

slATRMult = riskAmount == "Highest" ? 10 : riskAmount == "High" ? 8 : riskAmount == "Normal" ? 6.5 : riskAmount == "Low" ? 5 : riskAmount == "Lowest" ? 3 : customSLATRMult
tpPercent = input.float(0.3, "Fixed Take Profit %", step = 0.1, group = "TP / SL")
slPercent = input.float(0.4, "Fixed Stop Loss %", step = 0.1, group = "TP / SL")

// Enhanced R:R System
useEnhancedRR = input.bool(false, "Use Enhanced R:R System", group = "Enhanced R:R", tooltip = "Enable dynamic R:R calculation based on setup quality. When disabled, uses original 0.39 R:R.")
minRR = input.float(1.5, "Minimum R:R Ratio", minval = 1.0, maxval = 6.0, step = 0.1, group = "Enhanced R:R", tooltip = "Minimum risk-reward ratio for low quality setups")
maxRR = input.float(2.5, "Maximum R:R Ratio", minval = 1.5, maxval = 10.0, step = 0.1, group = "Enhanced R:R", tooltip = "Maximum risk-reward ratio for high quality setups")
requireVolumeConfirmation = input.bool(false, "Require Volume Confirmation", group = "Enhanced R:R", tooltip = "Require above-average volume for entry")
minQualityScore = input.float(1.0, "Minimum Quality Score", minval = 0.0, maxval = 5.0, step = 0.5, group = "Enhanced R:R", tooltip = "Minimum setup quality score required for entry (1-5 scale)")

// Time-Based Filtering System
useTimeFiltering = input.bool(false, "Enable Time Filtering", group = "Time Filtering", tooltip = "Enable time-based trade filtering to avoid low-quality trading periods")
avoidWeekends = input.bool(false, "Avoid Weekends", group = "Time Filtering", tooltip = "Skip trades on Saturday and Sunday (important for forex markets)")
avoidLunchHours = input.bool(false, "Avoid Lunch Hours (12:00-13:00)", group = "Time Filtering", tooltip = "Skip trades during lunch hour when volatility is typically low")
avoidEarlyMorning = input.bool(false, "Avoid Early Morning (00:00-06:00)", group = "Time Filtering", tooltip = "Skip trades during early morning hours with low market activity")
useCustomTimeRange = input.bool(false, "Use Custom Time Range", group = "Time Filtering", tooltip = "Define specific trading hours (e.g., 9-17 for business hours)")
customTimeStart = input.int(9, "Trading Start Hour", minval = 0, maxval = 23, step = 1, group = "Time Filtering", tooltip = "Hour to start taking trades (24-hour format)")
customTimeEnd = input.int(17, "Trading End Hour", minval = 0, maxval = 23, step = 1, group = "Time Filtering", tooltip = "Hour to stop taking trades (24-hour format)")

// Major Trading Session Filters
tradeLondonOnly = input.bool(false, "Trade London Session Only (08:00-16:00)", group = "Time Filtering", tooltip = "Only trade during London session hours (high liquidity for EUR/GBP pairs)")
tradeNewYorkOnly = input.bool(false, "Trade New York Session Only (13:00-21:00)", group = "Time Filtering", tooltip = "Only trade during New York session hours (high liquidity for USD pairs)")
avoidAsianSession = input.bool(false, "Avoid Asian Session (21:00-06:00)", group = "Time Filtering", tooltip = "Avoid Asian session (typically lower volatility for major pairs)")

// Day of Week Filters
avoidMondays = input.bool(false, "Avoid Mondays", group = "Time Filtering", tooltip = "Skip Monday trades (potential gaps, lower volume after weekend)")
avoidFridays = input.bool(false, "Avoid Fridays", group = "Time Filtering", tooltip = "Skip Friday trades (position squaring, news risk before weekend)")

// Session Timing Filters
avoidFirstHour = input.bool(false, "Avoid First Hour (09:00-10:00)", group = "Time Filtering", tooltip = "Avoid first hour after market open (high volatility, potential whipsaws)")
avoidLastHour = input.bool(false, "Avoid Last Hour (16:00-17:00)", group = "Time Filtering", tooltip = "Avoid last hour before major session close (position adjustments)")

// Original DynamicRR for backward compatibility
DynamicRR = DEBUG ? input.float(0.39, "[DBG] Dynamic Risk:Reward Ratio", group = "Debug") : 0.39

// Enhanced Alert System - Phase 9
buyAlertEnabled = input.bool(true, "Buy Signal", inline = "BS", group = "Alerts")
sellAlertEnabled = input.bool(true, "Sell Signal", inline = "BS", group = "Alerts")
tpAlertEnabled = input.bool(true, "Take-Profit Signal", inline = "TS", group = "Alerts")
slAlertEnabled = input.bool(true, "Stop-Loss Signal ", inline = "TS", group = "Alerts")

// Advanced Alert Settings
enableWebhookFormat = input.bool(false, "Enable Webhook Format", group = "Alerts", tooltip = "Format alerts for broker integration with JSON-like structure")
enableDetailedAlerts = input.bool(true, "Enable Detailed Alerts", group = "Alerts", tooltip = "Include additional information like R:R, quality scores, and trade details")
enableErrorAlerts = input.bool(true, "Enable Error Alerts", group = "Alerts", tooltip = "Send alerts for entry rejections and system errors")
enableStatusAlerts = input.bool(false, "Enable Status Alerts", group = "Alerts", tooltip = "Send alerts for trade status changes and system events")

// Alert Message Customization
alertPrefix = input.string("CRT", "Alert Prefix", group = "Alerts", tooltip = "Prefix for all alert messages")
includeSymbol = input.bool(true, "Include Symbol", group = "Alerts", tooltip = "Include trading symbol in alert messages")
includeTimeframe = input.bool(true, "Include Timeframe", group = "Alerts", tooltip = "Include current timeframe in alert messages")

// FVG Settings (Internal - not exposed as inputs since they're hardcoded in indicator)
fvgEnabled = true
fvgVolumetricInfo = false
fvgEndMethod = "Close"
fvgFilterMethod = "Average Range"
volumeThresholdPercent = 50
fvgBars = "Same Type"
fvgSensEnabled = true
combineFVGs = false
var bool allowGaps = true  // Allow gaps in FVG detection (from indicator)
deleteUntouched = true
deleteUntouchedAfterXBars = 200

// Order Block Settings (Internal)
OBsEnabled = true
orderBlockVolumetricInfo = false
obEndMethod = "Close"

// Breaker Block Settings (Internal)
BBsEnabled = false
breakBlockVolumetricInfo = false
bbEndMethod = "Wick"
breakersFull = true

// IFVG Settings (Internal)
ifvgEnabled = false
ifvgVolumetricInfo = false
ifvgEndMethod = "Wick"
ifvgFull = true

// Debug Settings (Internal)
maxATRMult = 3.5
extendZonesBy = 15
extendZonesDynamic = true
extendLastFVGs = true
changeCombinedFVGsColor = false
combinedText = false
startZoneFrom = "Last Bar"
volumeBarsPlace = "Left"
mirrorVolumeBars = true
showInvalidated = true

// Label size for debug (Internal)
dbgLabelSize = "Normal"
lblSize = size.normal
dbgShowBBFVG = false
//#endregion

//#region Parameter Validation
// Timeframe validation
var tfCheck = false
if not tfCheck
    if timeframe.in_seconds() > timeframe.in_seconds(higherTF)
        runtime.error("Higher timeframe must be higher than current timeframe.")
    tfCheck := true

// R:R validation
if useEnhancedRR and minRR >= maxRR
    runtime.error("Minimum R:R must be less than Maximum R:R")

// Quality score validation
if minQualityScore < 0 or minQualityScore > 5
    runtime.error("Quality score must be between 0 and 5")

// Time range validation
if useCustomTimeRange and customTimeStart >= customTimeEnd
    runtime.error("Trading start hour must be less than end hour")

// Strategy settings validation
if defaultQty <= 0 or defaultQty > 100
    runtime.error("Position size must be between 1 and 100 percent")

if commissionRate < 0 or commissionRate > 5
    runtime.error("Commission rate must be between 0 and 5 percent")
//#endregion

//#region Logging System
// Strategy logging function
logStrategy(message) =>
    if DEBUG
        log.info("[CRT Strategy] " + message)

// Log strategy initialization
logStrategy("CRT Strategy initialized with settings:")
logStrategy("Higher TF: " + higherTF + ", Entry Mode: " + entryMode + ", TP/SL Method: " + tpslMethod)
//#endregion

//#region User Defined Types (UDTs) - Phase 3

// Order Block Information Type (Core data only - visual elements removed)
type orderBlockInfo
    float top
    float bottom
    float obVolume
    string obType
    int startTime
    float bbVolume
    float obLowVolume
    float obHighVolume
    bool breaker = false
    int breakTime
    int breakerEndTime
    string timeframeStr
    bool disabled = false
    string combinedTimeframesStr = na
    bool combined = false

// Order Block Type (Visual elements removed for strategy)
type orderBlock
    orderBlockInfo info
    bool isActive = true  // Replaces isRendered for strategy logic

// Fair Value Gap Information Type (Core data only)
type FVGInfo
    float max = na
    float min = na
    bool isBull = na
    int t = na
    float totalVolume = na
    int startBarIndex = na
    int endBarIndex = na
    int startTime = na
    int endTime = na
    bool extendInfinite = false
    bool combined = false
    string combinedTimeframesStr = na
    bool disabled = false
    string timeframeStr = na

    float lowVolume = na
    float highVolume = na
    bool isInverse = false
    int lastTouched = na
    int lastTouchedIFVG = na
    int inverseEndIndex = na
    int inverseEndTime = na
    float inverseVolume

// Fair Value Gap Type (Visual elements removed for strategy)
type FVG
    FVGInfo info = na
    bool isActive = true  // Replaces isRendered for strategy logic

// Main CRT State Management Type (Partial profit fields excluded as requested)
type CRT
    string state
    int startTime

    string overlapDirection
    int bulkyTimeLow
    int bulkyTimeHigh
    float bulkyHigh
    float bulkyLow
    int breakTime

    FVG fvg
    int fvgEndTime
    orderBlock ob

    float slTarget
    float tpTarget
    string entryType
    int entryTime
    int exitTime
    float entryPrice
    float exitPrice
    int dayEndedBeforeExit

    // Enhanced R:R fields (kept)
    float qualityScore = na
    float dynamicRR = na

    // Partial Profit Taking fields - EXCLUDED as requested
    // float partialTP1 = na
    // float partialTP2 = na
    // bool tp1Hit = false
    // bool tp2Hit = false
    // float remainingPosition = 100.0
    // float trailingStop = na
    // bool trailingActive = false
    // int tp1HitTime = na
    // int tp2HitTime = na

    // Win/Loss Visual System fields - EXCLUDED as requested
    // string tradeResult = na
    // float actualRR = na
    // bool showResult = false

//#endregion

//#region UDT Helper Functions - Phase 3

// Create FVGInfo instance
createFVGInfo(h, l, bull, t, tv) =>
    FVGInfo newFVGInfo = FVGInfo.new(h, l, bull, t, tv)
    newFVGInfo

// Copy FVGInfo instance (from indicator)
method copy(FVGInfo this) =>
    FVGInfo.new(this.max, this.min, this.isBull, this.startTime, this.totalVolume)

// Create FVG instance
createFVG(FVGInfo FVGInfoF) =>
    FVG newFVG = FVG.new(FVGInfoF)
    newFVG

// Safe cleanup for FVG (visual elements removed, only data cleanup)
safeCleanupFVG(FVG fvg) =>
    fvg.isActive := false
    // Note: No visual elements to delete in strategy version

// Create Order Block instance
createOrderBlock(orderBlockInfo info) =>
    orderBlock newOB = orderBlock.new(info)
    newOB

// Copy orderBlockInfo instance (from indicator)
method copy(orderBlockInfo this) =>
    orderBlockInfo.new(this.top, this.bottom, this.obVolume, this.obType, this.startTime)

// Safe cleanup for Order Block (visual elements removed, only data cleanup)
safeCleanupOrderBlock(orderBlock ob) =>
    ob.isActive := false
    // Note: No visual elements to delete in strategy version

// Array management functions
arrHasFVG(FVG[] arr, FVG fvgF) =>
    hasFVG = false
    if arr.size() > 0
        for i = 0 to arr.size() - 1
            FVG fvg1 = arr.get(i)
            if fvg1.info.startTime == fvgF.info.startTime
                hasFVG := true
                break
    hasFVG

arrHasIFVG(FVG[] arr, FVG fvgF) =>
    hasIFVG = false
    if arr.size() > 0
        for i = 0 to arr.size() - 1
            FVG fvg1 = arr.get(i)
            if fvg1.info.isInverse
                if fvg1.info.startTime == fvgF.info.startTime
                    hasIFVG := true
                    break
    hasIFVG

// Check if array contains specific Order Block (by comparing key properties)
arrHasOB(array<orderBlock> arr, orderBlock targetOB) =>
    found = false
    if arr.size() > 0
        for i = 0 to arr.size() - 1
            currentOB = arr.get(i)
            if math.abs(currentOB.info.top - targetOB.info.top) < 0.0001 and
               math.abs(currentOB.info.bottom - targetOB.info.bottom) < 0.0001 and
               currentOB.info.obType == targetOB.info.obType and
               currentOB.info.startTime == targetOB.info.startTime
                found := true
                break
    found

//#endregion

//#region State Management Variables and Arrays - Phase 3

// Core state management arrays
var FVGInfo[] FVGInfoList = array.new<FVGInfo>(0)
var FVG[] allFVGList = array.new<FVG>(0)
var orderBlockInfo[] orderBlockInfoList = array.new<orderBlockInfo>(0)
var orderBlock[] allOrderBlockList = array.new<orderBlock>(0)

// CRT state management
var CRT[] crtList = array.new<CRT>(0)
var CRT lastCRT = na

// Additional state tracking variables
var lineX = array.new<line>()
var boxX = array.new<box>()
var labelX = array.new<label>()

// ATR calculation for CRT logic
atr = ta.atr(atrLen)
atrCRT = atr

// FVG sensitivity mapping
var float fvgSensitivity = fvgSensitivityText == "All" ? 0.1 : fvgSensitivityText == "Extreme" ? 0.3 : fvgSensitivityText == "High" ? 0.5 : fvgSensitivityText == "Normal" ? 0.7 : 0.9

// Volume check flag
volCheck = fvgVolumetricInfo

// allowGaps already defined above

//#endregion

//#region Data Validation Functions - Phase 3

// Validate FVG data integrity
isFVGValid(FVGInfo FVGInfoF) =>
    valid = true
    if (not showInvalidated) and (not na(FVGInfoF.endTime))
        valid := false
    else if FVGInfoF.disabled
        valid := false
    valid

// Validate IFVG data integrity
isIFVGValid(FVGInfo FVGInfoF) =>
    valid = true
    if not ifvgEnabled
        valid := false
    else if (not showInvalidated) and (not na(FVGInfoF.inverseEndTime))
        valid := false
    else if FVGInfoF.disabled
        valid := false
    valid

// Validate FVG in timeframe context
isFVGValidInTimeframe(FVGInfo FVGInfoF) =>
    valid = true
    if (not showInvalidated) and (not na(FVGInfoF.endTime))
        valid := false
    else if FVGInfoF.disabled
        valid := false
    else if not na(FVGInfoF.endBarIndex) and (FVGInfoF.endBarIndex - FVGInfoF.startBarIndex) < minimumFVGSize
        valid := false
    else if na(FVGInfoF.endBarIndex) and deleteUntouched and (bar_index - FVGInfoF.lastTouched) > deleteUntouchedAfterXBars
        valid := false
    valid

// Validate IFVG in timeframe context
isIFVGValidInTimeframe(FVGInfo FVGInfoF) =>
    valid = true
    if not ifvgEnabled
        valid := false
    else if (not showInvalidated) and (not na(FVGInfoF.inverseEndIndex))
        valid := false
    else if not na(FVGInfoF.inverseEndIndex) and (FVGInfoF.inverseEndIndex - FVGInfoF.endBarIndex) < minimumIFVGSize
        valid := false
    else if na(FVGInfoF.inverseEndIndex) and deleteUntouched and (bar_index - FVGInfoF.lastTouchedIFVG) > deleteUntouchedAfterXBars
        valid := false
    valid

// Validate Order Block data integrity
isOrderBlockValid(orderBlockInfo obInfo) =>
    valid = true
    if obInfo.disabled
        valid := false
    else if not na(obInfo.breakTime)
        valid := false
    valid

// Validate CRT state integrity
isCRTValid(CRT crt) =>
    valid = true
    if na(crt) or crt.state == "Aborted"
        valid := false
    valid

//#endregion

//#region Memory Management Functions - Phase 3

// Clean up old FVGs to manage memory
cleanupOldFVGs() =>
    if allFVGList.size() > 50  // Limit to 50 active FVGs
        for i = allFVGList.size() - 1 to 50
            oldFVG = allFVGList.get(i)
            safeCleanupFVG(oldFVG)
            allFVGList.remove(i)

// Clean up old Order Blocks to manage memory
cleanupOldOrderBlocks() =>
    if allOrderBlockList.size() > maxOrderBlocks
        for i = allOrderBlockList.size() - 1 to maxOrderBlocks
            oldOB = allOrderBlockList.get(i)
            safeCleanupOrderBlock(oldOB)
            allOrderBlockList.remove(i)

// Clean up old CRT entries to manage memory
cleanupOldCRTs() =>
    if crtList.size() > 10  // Limit to 10 CRT entries
        for i = crtList.size() - 1 to 10
            crtList.remove(i)

// Periodic memory cleanup
performMemoryCleanup() =>
    if bar_index % 100 == 0  // Every 100 bars
        cleanupOldFVGs()
        cleanupOldOrderBlocks()
        cleanupOldCRTs()

//#endregion

//#region Utility Functions - Phase 3

// Calculate percentage difference between two values
diffPercent(float val1, float val2) =>
    (math.abs(val1 - val2) / val2) * 100.0

// Find value and return time (simplified for strategy - original used complex arrays)
findValRtnTime(valToFind, toSearch, searchMode) =>
    int rtnTime = na
    // Simplified implementation for strategy - return current time as fallback
    rtnTime := time
    rtnTime

//#endregion

//#region FVG Analysis Functions - Phase 3

// Calculate area of FVG for overlap detection
areaOfFVG(FVGInfo FVGInfoF) =>
    float XA1 = FVGInfoF.startTime
    float XA2 = na(FVGInfoF.endTime) ? time + 1 : FVGInfoF.endTime
    float YA1 = FVGInfoF.max
    float YA2 = FVGInfoF.min
    float edge1 = math.sqrt((XA2 - XA1) * (XA2 - XA1) + (YA2 - YA2) * (YA2 - YA2))
    float edge2 = math.sqrt((XA2 - XA2) * (XA2 - XA2) + (YA2 - YA1) * (YA2 - YA1))
    float totalArea = edge1 * edge2
    totalArea

// Check if two FVGs overlap
doFVGsTouch(FVGInfo FVGInfo1, FVGInfo FVGInfo2) =>
    float XA1 = FVGInfo1.startTime
    float XA2 = na(FVGInfo1.endTime) ? time + 1 : FVGInfo1.endTime
    float YA1 = FVGInfo1.max
    float YA2 = FVGInfo1.min

    float XB1 = FVGInfo2.startTime
    float XB2 = na(FVGInfo2.endTime) ? time + 1 : FVGInfo2.endTime
    float YB1 = FVGInfo2.max
    float YB2 = FVGInfo2.min
    float intersectionArea = math.max(0, math.min(XA2, XB2) - math.max(XA1, XB1)) * math.max(0, math.min(YA1, YB1) - math.max(YA2, YB2))
    float unionArea = areaOfFVG(FVGInfo1) + areaOfFVG(FVGInfo2) - intersectionArea

    float overlapPercentage = (intersectionArea / unionArea) * 100.0

    if overlapPercentage > overlapThresholdPercentage
        true
    else
        false

//#endregion

//#region Enhanced R:R and Quality Functions - Phase 3

// Calculate setup quality score (1-5 scale)
calculateQualityScore(crt) =>
    score = 0.0

    // Base score for valid setup
    score += 1.0

    // HTF candle size bonus
    if not na(crt.bulkyHigh) and not na(crt.bulkyLow)
        htfRange = crt.bulkyHigh - crt.bulkyLow
        if htfRange > atr * bulkyCandleATR
            score += 1.0

    // FVG/OB quality bonus
    if entryMode == "FVGs" and not na(crt.fvg)
        fvgSize = crt.fvg.info.max - crt.fvg.info.min
        if fvgSize > atr * 0.5
            score += 0.5
        if crt.fvg.info.totalVolume > ta.sma(volume, 20)
            score += 0.5
    else if entryMode == "Order Blocks" and not na(crt.ob)
        obSize = crt.ob.info.top - crt.ob.info.bottom
        if obSize > atr * 0.5
            score += 0.5
        if crt.ob.info.obVolume > ta.sma(volume, 20)
            score += 0.5

    // Volume confirmation bonus
    if volume > ta.sma(volume, 20) * 1.2
        score += 1.0

    // Time-based quality bonus
    currentHour = hour(time)
    if currentHour >= 8 and currentHour <= 16  // London/NY overlap
        score += 0.5

    math.min(score, 5.0)  // Cap at 5.0

// Calculate dynamic R:R based on quality
calculateDynamicRR(qualityScore) =>
    if useEnhancedRR
        // Linear interpolation between minRR and maxRR based on quality
        normalizedQuality = (qualityScore - 1.0) / 4.0  // Normalize 1-5 to 0-1
        dynamicRatio = minRR + (maxRR - minRR) * normalizedQuality
        math.max(minRR, math.min(maxRR, dynamicRatio))
    else
        DynamicRR  // Use original fixed ratio

// Validate entry based on quality score
isValidEntry(qualityScore) =>
    if useEnhancedRR
        qualityScore >= minQualityScore
    else
        true

//#endregion

//#region Volume and Time Validation - Phase 3

// Validate volume confirmation (works independently)
isVolumeValid() =>
    if requireVolumeConfirmation
        avgVol = ta.sma(volume, 20)
        volume >= avgVol * 1.1
    else
        true

// Validate time-based filtering (works independently)
isValidTradingTime() =>
    if not useTimeFiltering
        true
    else
        valid = true
        currentHour = hour(time)
        currentDayOfWeek = dayofweek(time)

        // Weekend filter
        if avoidWeekends and (currentDayOfWeek == dayofweek.saturday or currentDayOfWeek == dayofweek.sunday)
            valid := false

        // Lunch hours filter (12:00-13:00)
        if avoidLunchHours and (currentHour >= 12 and currentHour < 13)
            valid := false

        // Early morning filter (00:00-06:00)
        if avoidEarlyMorning and (currentHour >= 0 and currentHour < 6)
            valid := false

        // Custom time range filter
        if useCustomTimeRange and (currentHour < customTimeStart or currentHour >= customTimeEnd)
            valid := false

        // London session filter
        if tradeLondonOnly and not (currentHour >= 8 and currentHour < 16)
            valid := false

        // New York session filter
        if tradeNewYorkOnly and not (currentHour >= 13 and currentHour < 21)
            valid := false

        // Asian session filter
        if avoidAsianSession and ((currentHour >= 21 and currentHour <= 23) or (currentHour >= 0 and currentHour < 6))
            valid := false

        // Day of week filters
        if avoidMondays and currentDayOfWeek == dayofweek.monday
            valid := false

        if avoidFridays and currentDayOfWeek == dayofweek.friday
            valid := false

        // Session timing filters
        if avoidFirstHour and (currentHour >= 9 and currentHour < 10)
            valid := false

        if avoidLastHour and (currentHour >= 16 and currentHour < 17)
            valid := false

        valid

//#endregion

//#region Higher Timeframe Analysis - Phase 4

// Bar information type for HTF analysis (migrated from indicator)
type barInfo
    float o
    float h
    float l
    float c
    float tr
    float atr

// HTF data variables
var float htfLastHigh = na
var float htfLastLow = na
var bool newBulkyCandle = false

// Current bar information
curBar = barInfo.new(open, high, low, close, ta.tr, atr)

// Get higher timeframe data using request.security
higherTFBar = request.security(syminfo.tickerid, higherTF, curBar)
oldHigherTFBar = higherTFBar[1]

// Detect new bulky candle (core CRT logic)
detectNewBulkyCandle() =>
    newBulky = false
    float detectedHigh = na
    float detectedLow = na
    if not na(oldHigherTFBar)
        // Check for new HTF bar formation AND size criteria
        if oldHigherTFBar.h != higherTFBar.h and (higherTFBar.tr > higherTFBar.atr * bulkyCandleATR)
            newBulky := true
            detectedHigh := higherTFBar.h
            detectedLow := higherTFBar.l
            logStrategy("New bulky candle detected: High=" + str.tostring(detectedHigh) + ", Low=" + str.tostring(detectedLow) + ", TR=" + str.tostring(higherTFBar.tr) + ", ATR=" + str.tostring(higherTFBar.atr))
    [newBulky, detectedHigh, detectedLow]

// Update bulky candle detection
[newBulkyDetected, detectedHigh, detectedLow] = detectNewBulkyCandle()
newBulkyCandle := newBulkyDetected
if newBulkyDetected
    htfLastHigh := detectedHigh
    htfLastLow := detectedLow

// HTF candle validation functions
isHTFDataValid() =>
    not na(higherTFBar) and not na(higherTFBar.h) and not na(higherTFBar.l) and not na(higherTFBar.atr)

getHTFCandleSize() =>
    if isHTFDataValid()
        higherTFBar.h - higherTFBar.l
    else
        0.0

isHTFCandleBulky() =>
    if isHTFDataValid()
        higherTFBar.tr > higherTFBar.atr * bulkyCandleATR
    else
        false

// HTF candle break detection
checkBulkyBreak(bulkyHigh, bulkyLow) =>
    bearOverlap = false
    bullOverlap = false

    // Check for bearish overlap (price breaks above and closes below)
    if high > bulkyHigh and close <= bulkyHigh
        bearOverlap := true

    // Check for bullish overlap (price breaks below and closes above)
    if low < bulkyLow and close >= bulkyLow
        bullOverlap := true

    [bearOverlap, bullOverlap]

// HTF timing functions (migrated from indicator)
getHTFCandleTimeLow(targetLow) =>
    findValRtnTime(targetLow, "Low", "Nearest")

getHTFCandleTimeHigh(targetHigh) =>
    findValRtnTime(targetHigh, "High", "Nearest")

// HTF candle quality assessment
assessHTFCandleQuality() =>
    quality = 0.0

    if isHTFDataValid()
        candleSize = getHTFCandleSize()
        atrMultiple = candleSize / higherTFBar.atr

        // Quality based on ATR multiple
        if atrMultiple >= 3.0
            quality += 2.0
        else if atrMultiple >= 2.5
            quality += 1.5
        else if atrMultiple >= 2.0
            quality += 1.0
        else if atrMultiple >= 1.5
            quality += 0.5

        // Bonus for very large candles
        if atrMultiple >= 4.0
            quality += 1.0

    quality

// HTF candle direction analysis
getHTFCandleDirection() =>
    direction = "Neutral"
    if isHTFDataValid()
        if higherTFBar.c > higherTFBar.o
            direction := "Bullish"
        else if higherTFBar.c < higherTFBar.o
            direction := "Bearish"
    direction

// HTF candle body vs wick analysis
getHTFCandleStructure() =>
    bodySize = 0.0
    upperWick = 0.0
    lowerWick = 0.0

    if isHTFDataValid()
        bodySize := math.abs(higherTFBar.c - higherTFBar.o)
        upperWick := higherTFBar.h - math.max(higherTFBar.c, higherTFBar.o)
        lowerWick := math.min(higherTFBar.c, higherTFBar.o) - higherTFBar.l

    [bodySize, upperWick, lowerWick]

//#endregion

//#region FVG Detection System - Phase 5

// FVG detection variables
var bool newBearishFVG = false
var bool newBullishFVG = false
var FVGInfo newFVGInfo = na

// Core FVG detection function (exact copy from indicator)
detectFVGs() =>
    bearFVG = false
    bullFVG = false
    bearCondition = false
    bullCondition = false

    // Volume analysis for FVG validation
    volSum3 = math.sum(volume, 3)
    shortTerm = ta.sma(volume, 10)
    longTerm = ta.sma(volume, 20)

    // Bar size analysis (exact from indicator)
    barSizeSum = (high - low) + (high[1] - low[1]) + (high[2] - low[2])
    fvgBarsCheck = true
    barSizeCheck = true

    // Volume and size conditions (exact from indicator)
    if fvgBarsCheck and barSizeCheck
        maxCODiff = math.max(math.abs(close[2] - open[1]), math.abs(close[1] - open))
        if fvgFilterMethod == "Average Range"
            bearCondition := ((not fvgSensEnabled) or (barSizeSum * fvgSensitivity > atr / 1.5)) and (allowGaps or (maxCODiff <= atr))
            bullCondition := ((not fvgSensEnabled) or (barSizeSum * fvgSensitivity > atr / 1.5)) and (allowGaps or (maxCODiff <= atr))
        else if fvgFilterMethod == "Volume Threshold"
            thresholdMultiplier = (volumeThresholdPercent / 100.0)
            bearCondition := shortTerm > longTerm * thresholdMultiplier and (allowGaps or (maxCODiff <= atr))
            bullCondition := shortTerm > longTerm * thresholdMultiplier and (allowGaps or (maxCODiff <= atr))

    // Core 3-candle FVG pattern detection (exact from indicator)
    bearFVG := high < low[2] and close[1] < low[2] and bearCondition
    bullFVG := low > high[2] and close[1] > high[2] and bullCondition

    // Calculate FVG properties (exact from indicator)
    float totalVolume = volCheck ? volSum3 : 0
    FVGInfo newFVGInfo = bearFVG ? createFVGInfo(low[2], high, false, time, totalVolume) : bullFVG ? createFVGInfo(low, high[2], true, time, totalVolume) : na
    FVGSize = bearFVG ? math.abs(low[2] - high) : bullFVG ? math.abs(low - high[2]) : 0

    // Size validation using sensitivity (exact from indicator)
    FVGSizeEnough = (FVGSize * fvgSensitivity > atr)

    // Return values for strategy processing
    [bearFVG, bullFVG, newFVGInfo, FVGSizeEnough]

// Handle FVGs final processing (from indicator)
handleFVGsFinal() =>
    string alertTimeFVG = ""
    bool newBullishFVGAlert = false
    bool newBearishFVGAlert = false
    string alertTimeIFVG = ""
    bool newBullishIFVGAlert = false
    bool newBearishIFVGAlert = false

    FVG[] fvgsToAdd = array.new<FVG>(0)

    if not na(FVGInfoList)
        if FVGInfoList.size() > 0
            for j = 0 to FVGInfoList.size() - 1
                FVGInfoF = FVGInfoList.get(j)
                FVGInfoF.timeframeStr := ""
                fvgsToAdd.unshift(createFVG(FVGInfo.copy(FVGInfoF)))

    // Check New FVGs
    if fvgsToAdd.size() > 0
        for i = 0 to fvgsToAdd.size() - 1
            fvgToTest = fvgsToAdd.get(i)
            if not arrHasFVG(allFVGList, fvgToTest)
                alertTimeFVG := fvgToTest.info.timeframeStr
                if fvgToTest.info.isBull
                    newBullishFVGAlert := true
                else
                    newBearishFVGAlert := true

    // Delete Old FVGs
    if allFVGList.size() > 0
        for i = 0 to allFVGList.size() - 1
            safeCleanupFVG(allFVGList.get(i))
    allFVGList.clear()

    // Add new FVGs
    if fvgsToAdd.size() > 0
        for i = 0 to fvgsToAdd.size() - 1
            allFVGList.unshift(fvgsToAdd.get(i))

    [alertTimeFVG, newBullishFVGAlert, newBearishFVGAlert, alertTimeIFVG, newBullishIFVGAlert, newBearishIFVGAlert]

// Handle Order Blocks final processing (from indicator)
handleOrderBlocksFinal() =>
    string alertTimeOB = ""
    string alertTimeBB = ""
    bool newBullishOBAlert = false
    bool newBearishOBAlert = false
    bool newBullishBBAlert = false
    bool newBearishBBAlert = false

    orderBlock[] orderBlocksToAdd = array.new<orderBlock>(0)

    if not na(orderBlockInfoList)
        if orderBlockInfoList.size() > 0
            for j = 0 to orderBlockInfoList.size() - 1
                orderBlockInfoF = orderBlockInfoList.get(j)
                orderBlockInfoF.timeframeStr := ""
                orderBlocksToAdd.unshift(createOrderBlock(orderBlockInfo.copy(orderBlockInfoF)))

    // Check New Order & Breaker Blocks
    if orderBlocksToAdd.size() > 0
        for i = 0 to orderBlocksToAdd.size() - 1
            obToTest = orderBlocksToAdd.get(i)
            if obToTest.info.breaker == false
                if not arrHasOB(allOrderBlockList, obToTest)
                    alertTimeOB := obToTest.info.timeframeStr
                    if obToTest.info.obType == "Bull"
                        newBullishOBAlert := true
                    else
                        newBearishOBAlert := true
            else
                if not arrHasOB(allOrderBlockList, obToTest)
                    alertTimeBB := obToTest.info.timeframeStr
                    if obToTest.info.obType == "Bull"
                        newBearishBBAlert := true
                    else
                        newBullishBBAlert := true

    // Delete Old Order Blocks
    if allOrderBlockList.size() > 0
        for i = 0 to allOrderBlockList.size() - 1
            safeCleanupOrderBlock(allOrderBlockList.get(i))
    allOrderBlockList.clear()

    // Add new Order Blocks
    if orderBlocksToAdd.size() > 0
        for i = 0 to orderBlocksToAdd.size() - 1
            allOrderBlockList.unshift(orderBlocksToAdd.get(i))

    [alertTimeOB, alertTimeBB, newBullishOBAlert, newBearishOBAlert, newBullishBBAlert, newBearishBBAlert]

// Order Block swing type
type obSwing
    int x
    float y
    bool crossed = false

// Find Order Blocks function (exact copy from indicator)
findOrderBlocks() =>
    if (bar_index > last_bar_index - maxDistanceToLastBar and barstate.isconfirmed) and (OBsEnabled or BBsEnabled)
        upper = ta.highest(swingLength)
        lower = ta.lowest(swingLength)

        // swingType, top, btm variables are now declared outside function

        li = low[swingLength]
        hi = high[swingLength]
        bi = bar_index[swingLength]

        swingType := hi > upper ? 0 : li < lower ? 1 : swingType

        if swingType == 0 and swingType[1] != 0
            top := obSwing.new(bi, hi)

        if swingType == 1 and swingType[1] != 1
            btm := obSwing.new(bi, li)

        boxTopBull = high[1]
        boxBtmBull = low[1]
        boxLocBull = bar_index[1]

        boxTopBear = high[1]
        boxBtmBear = low[1]
        boxLocBear = bar_index[1]

        // Update existing Order Blocks for breaker detection
        if allOrderBlockList.size() > 0
            for i = 0 to allOrderBlockList.size() - 1
                currentOB = allOrderBlockList.get(i)
                if currentOB.isActive and not currentOB.info.breaker
                    if currentOB.info.obType == "Bull"
                        if (bbEndMethod == "Wick" ? high : close) > currentOB.info.top and na(currentOB.info.breakerEndTime)
                            currentOB.info.breakerEndTime := time_close
                    else
                        if (bbEndMethod == "Wick" ? low : close) < currentOB.info.bottom and na(currentOB.info.breakerEndTime)
                            currentOB.info.breakerEndTime := time_close

        // Bullish Order Block
        if close > top.y and not top.crossed
            top.crossed := true
            obSize = math.abs(boxTopBull - boxBtmBull)
            if obSize <= atr * maxATRMult
                newOrderBlockInfo = orderBlockInfo.new(boxTopBull, boxBtmBull, volume + volume[1] + volume[2], "Bull", boxLocBull)
                newOrderBlockInfo.obLowVolume := volume[2]
                newOrderBlockInfo.obHighVolume := volume + volume[1]
                orderBlockInfoList.unshift(newOrderBlockInfo)
                if orderBlockInfoList.size() > maxOrderBlocks
                    orderBlockInfoList.pop()

        // Bearish Order Block
        if close < btm.y and not btm.crossed
            btm.crossed := true
            obSize = math.abs(boxTopBear - boxBtmBear)
            if obSize <= atr * maxATRMult
                newOrderBlockInfo = orderBlockInfo.new(boxTopBear, boxBtmBear, volume + volume[1] + volume[2], "Bear", boxLocBear)
                newOrderBlockInfo.obLowVolume := volume + volume[1]
                newOrderBlockInfo.obHighVolume := volume[2]
                orderBlockInfoList.unshift(newOrderBlockInfo)
                if orderBlockInfoList.size() > maxOrderBlocks
                    orderBlockInfoList.pop()
    orderBlockInfoList

// FVG invalidation detection
checkFVGInvalidation(FVGInfo fvgInfo) =>
    invalidated = false
    if not na(fvgInfo) and na(fvgInfo.endTime)
        if fvgEndMethod == "Close"
            if fvgInfo.isBull and close < fvgInfo.min
                invalidated := true
            else if not fvgInfo.isBull and close > fvgInfo.max
                invalidated := true
        else if fvgEndMethod == "Wick"
            if fvgInfo.isBull and low < fvgInfo.min
                invalidated := true
            else if not fvgInfo.isBull and high > fvgInfo.max
                invalidated := true
    invalidated

// FVG retracement detection
checkFVGRetracement(FVGInfo fvgInfo) =>
    retraced = false
    if not na(fvgInfo) and na(fvgInfo.endTime)
        if fvgInfo.isBull and low <= fvgInfo.max
            retraced := true
        else if not fvgInfo.isBull and high >= fvgInfo.min
            retraced := true
    retraced

// Update existing FVGs for invalidation
updateActiveFVGs() =>
    if allFVGList.size() > 0
        for i = 0 to allFVGList.size() - 1
            currentFVG = allFVGList.get(i)
            if currentFVG.isActive and na(currentFVG.info.endTime)
                if checkFVGInvalidation(currentFVG.info)
                    currentFVG.info.endTime := time
                    currentFVG.info.endBarIndex := bar_index
                    currentFVG.isActive := false
                    logStrategy("FVG invalidated at " + str.tostring(time))

// Get latest FVG of specific type
getLatestFVG(isBullish) =>
    FVGInfo foundFVG = na
    if allFVGList.size() > 0
        for i = 0 to allFVGList.size() - 1
            currentFVG = allFVGList.get(i)
            if currentFVG.isActive and currentFVG.info.isBull == isBullish and na(currentFVG.info.endTime)
                foundFVG := currentFVG.info
                break
    foundFVG

// FVG quality assessment
assessFVGQuality(FVGInfo fvgInfo) =>
    quality = 0.0
    if not na(fvgInfo)
        fvgSize = math.abs(fvgInfo.max - fvgInfo.min)
        atrRatio = fvgSize / atr

        // Size-based quality
        if atrRatio >= 2.0
            quality += 2.0
        else if atrRatio >= 1.5
            quality += 1.5
        else if atrRatio >= 1.0
            quality += 1.0
        else if atrRatio >= 0.5
            quality += 0.5

        // Volume-based quality
        if fvgInfo.totalVolume > ta.sma(volume, 20) * 1.5
            quality += 1.0
        else if fvgInfo.totalVolume > ta.sma(volume, 20)
            quality += 0.5

    quality

//#endregion

//#region Order Block Detection System - Phase 6

// Order Block detection now handled by findOrderBlocks() function (from indicator)

// Order Block detection variables (consolidated)
var float maxATRMult = 3.5  // Maximum ATR multiplier for OB size validation
var int time_close = time  // Close time for current bar

// Order Block swing detection variables (must be outside function to persist)
var int swingType = 0
var obSwing top = obSwing.new(na, na)
var obSwing btm = obSwing.new(na, na)

// Order Block invalidation detection (breaker block logic)
checkOBInvalidation(orderBlockInfo obInfo) =>
    invalidated = false
    if not na(obInfo) and not obInfo.breaker
        if obInfo.obType == "Bull"
            if obEndMethod == "Wick" ? low : math.min(open, close) < obInfo.bottom
                invalidated := true
        else
            if obEndMethod == "Wick" ? high : math.max(open, close) > obInfo.top
                invalidated := true
    invalidated

// Order Block retracement detection
checkOBRetracement(orderBlockInfo obInfo) =>
    retraced = false
    if not na(obInfo) and not obInfo.breaker
        if obInfo.obType == "Bull" and low <= obInfo.top
            retraced := true
        else if obInfo.obType == "Bear" and high >= obInfo.bottom
            retraced := true
    retraced

// Update existing Order Blocks for invalidation
updateActiveOrderBlocks() =>
    if allOrderBlockList.size() > 0
        for i = 0 to allOrderBlockList.size() - 1
            currentOB = allOrderBlockList.get(i)
            if currentOB.isActive and not currentOB.info.breaker
                if checkOBInvalidation(currentOB.info)
                    currentOB.info.breaker := true
                    currentOB.info.breakTime := time
                    currentOB.isActive := false
                    logStrategy("Order Block invalidated (became breaker) at " + str.tostring(time))

// Get latest Order Block of specific type
getLatestOrderBlock(isBullish) =>
    orderBlockInfo latestOB = na
    if allOrderBlockList.size() > 0
        for i = 0 to allOrderBlockList.size() - 1
            currentOB = allOrderBlockList.get(i)
            isBullishOB = (currentOB.info.obType == "Bull")
            if currentOB.isActive and isBullishOB == isBullish and not currentOB.info.breaker
                latestOB := currentOB.info
                break
    latestOB

// Order Block quality assessment
assessOBQuality(orderBlockInfo obInfo) =>
    quality = 0.0
    if not na(obInfo)
        obSize = math.abs(obInfo.top - obInfo.bottom)
        atrRatio = obSize / atr

        // Size-based quality
        if atrRatio >= 2.0
            quality += 2.0
        else if atrRatio >= 1.5
            quality += 1.5
        else if atrRatio >= 1.0
            quality += 1.0
        else if atrRatio >= 0.5
            quality += 0.5

        // Volume-based quality
        if obInfo.obVolume > ta.sma(volume, 20) * 1.5
            quality += 1.0
        else if obInfo.obVolume > ta.sma(volume, 20)
            quality += 0.5

    quality

//#endregion

// Remove duplicate ATR calculation (already defined above)
// atr already defined above
// volCheck already defined above

// FVG sensitivity already defined above - update with indicator values
fvgSensitivity := fvgSensitivityText == "All" ? 100 : fvgSensitivityText == "Extreme" ? 6 : fvgSensitivityText == "High" ? 2 : fvgSensitivityText == "Normal" ? 1.5 : 1
extendZonesByTime = extendZonesBy * timeframe.in_seconds(timeframe.period) * 1000

// Array management for price tracking (migrated from indicator)
const int maxArrCount = 200
var array<float> lo = array.new<float>()
var array<float> hi = array.new<float>()
var array<int> ti = array.new<int>()

// Update arrays on each bar
lo.unshift(low)
hi.unshift(high)
ti.unshift(time)
if lo.size() > maxArrCount
    lo.pop()
    hi.pop()
    ti.pop()

// Strategy status tracking
var string strategyStatus = "Initializing"
var int totalSignals = 0
var int rejectedSignals = 0
var int activeFVGs = 0
var int activeOrderBlocks = 0
var int activeCRTs = 0

// HTF tracking variables
var int totalBulkyCandles = 0
var int validBulkyCandles = 0
var float lastBulkySize = na
var string lastBulkyDirection = na
var int lastBulkyTime = na

// FVG tracking variables
var int totalFVGs = 0
var int validFVGs = 0
var int bearishFVGs = 0
var int bullishFVGs = 0
var int invalidatedFVGs = 0
var float lastFVGSize = na
var string lastFVGType = na

// Order Block tracking variables
var int totalOrderBlocks = 0
var int validOrderBlocks = 0
var int bearishOrderBlocks = 0
var int bullishOrderBlocks = 0
var int invalidatedOrderBlocks = 0
var float lastOBSize = na
var string lastOBType = na

// Entry tracking variables
var int totalEntries = 0
var int longEntries = 0
var int shortEntries = 0
var int fvgEntries = 0
var int obEntries = 0
var float lastEntryPrice = na
var string lastEntryType = na
var int lastEntryTime = na

// Exit tracking variables
var int totalExits = 0
var int takeProfitExits = 0
var int stopLossExits = 0
var float lastExitPrice = na
var string lastExitType = na
var int lastExitTime = na
var float totalRR = 0.0
var float avgRR = na

// Perform memory cleanup periodically
performMemoryCleanup()

// Process FVG detection using indicator's approach
[bearFVGDetected, bullFVGDetected, newFVGInfo, FVGSizeEnough] = detectFVGs()

// Add new FVG to FVGInfoList if detected and size is enough
if not na(newFVGInfo) and FVGSizeEnough
    // Add to FVGInfo list (matching indicator behavior)
    FVGInfoList.unshift(newFVGInfo)
    while FVGInfoList.size() > showLastXFVGs
        FVGInfoList.pop()

    // Update tracking counters
    totalFVGs += 1
    validFVGs += 1
    if newFVGInfo.isBull
        bullishFVGs += 1
        lastFVGType := "Bullish"
        newBullishFVG := true
    else
        bearishFVGs += 1
        lastFVGType := "Bearish"
        newBearishFVG := true

    lastFVGSize := math.abs(newFVGInfo.max - newFVGInfo.min)
    logStrategy("New FVG added to arrays - Total: " + str.tostring(totalFVGs))

// Process Order Blocks using indicator's approach
findOrderBlocks()

// Process FVGs and Order Blocks using indicator's final handlers
if barstate.isconfirmed and (bar_index > last_bar_index - maxDistanceToLastBar)
    [alertTimeFVG, newBullishFVGAlert, newBearishFVGAlert, alertTimeIFVG, newBullishIFVGAlert, newBearishIFVGAlert] = handleFVGsFinal()
    [alertTimeOB, alertTimeBB, newBullishOBAlert, newBearishOBAlert, newBullishBBAlert, newBearishBBAlert] = handleOrderBlocksFinal()

// Update existing FVGs for invalidation
updateActiveFVGs()

// Order Block processing now handled by findOrderBlocks() and handleOrderBlocksFinal()
// Update tracking counters based on allOrderBlockList size
totalOrderBlocks := allOrderBlockList.size()
bullishOrderBlocks := 0
bearishOrderBlocks := 0

if allOrderBlockList.size() > 0
    for i = 0 to allOrderBlockList.size() - 1
        currentOB = allOrderBlockList.get(i)
        if currentOB.info.obType == "Bull"
            bullishOrderBlocks += 1
        else
            bearishOrderBlocks += 1

// Update counters
activeFVGs := allFVGList.size()
activeOrderBlocks := allOrderBlockList.size()
activeCRTs := crtList.size()

// Update HTF tracking
if newBulkyCandle
    totalBulkyCandles += 1
    if isHTFCandleBulky()
        validBulkyCandles += 1
        lastBulkySize := getHTFCandleSize()
        lastBulkyDirection := getHTFCandleDirection()
        lastBulkyTime := time
        logStrategy("Valid bulky candle #" + str.tostring(validBulkyCandles) + " detected")

// HTF data validation and logging
if barstate.islast and DEBUG
    htfValid = isHTFDataValid()
    htfSize = getHTFCandleSize()
    htfBulky = isHTFCandleBulky()
    htfQuality = assessHTFCandleQuality()

    logStrategy("HTF Status - Valid: " + str.tostring(htfValid) + ", Size: " + str.tostring(htfSize) + ", Bulky: " + str.tostring(htfBulky) + ", Quality: " + str.tostring(htfQuality))

//#region CRT State Machine with HTF Integration - Phase 4

// CRT creation and management
var bool createNewCRT = true

// Main CRT logic (migrated from indicator with HTF integration)
if bar_index > last_bar_index - maxDistanceToLastBar and barstate.isconfirmed
    createNewCRT := true

    // Don't create new CRT if one is already active
    if not na(lastCRT)
        if na(lastCRT.exitPrice) and lastCRT.state != "Aborted"
            createNewCRT := false

    // Create new CRT instance
    if createNewCRT
        newCRT = CRT.new("Waiting For Bulky Candle", time)
        crtList.unshift(newCRT)
        lastCRT := newCRT
        logStrategy("New CRT created - Waiting For Bulky Candle")

    // Process active CRT
    if not na(lastCRT)
        // State 1: Waiting For Bulky Candle
        if lastCRT.state == "Waiting For Bulky Candle" and newBulkyCandle
            lastCRT.bulkyHigh := htfLastHigh
            lastCRT.bulkyLow := htfLastLow
            lastCRT.bulkyTimeLow := getHTFCandleTimeLow(htfLastLow)
            lastCRT.bulkyTimeHigh := getHTFCandleTimeHigh(htfLastHigh)

            // Calculate setup quality score (enhanced R:R system)
            if useEnhancedRR
                bulkyRange = math.abs(htfLastHigh - htfLastLow)
                lastCRT.qualityScore := calculateQualityScore(lastCRT)
                lastCRT.dynamicRR := calculateDynamicRR(lastCRT.qualityScore)
                logStrategy("Quality score: " + str.tostring(lastCRT.qualityScore) + ", Dynamic R:R: " + str.tostring(lastCRT.dynamicRR))
            else
                lastCRT.qualityScore := na
                lastCRT.dynamicRR := DynamicRR

            lastCRT.state := "Waiting For Bulky Candle Break"
            logStrategy("Bulky candle stored - Waiting For Break. High: " + str.tostring(htfLastHigh) + ", Low: " + str.tostring(htfLastLow))

        // State 2: Waiting For Bulky Candle Break
        if lastCRT.state == "Waiting For Bulky Candle Break"
            if lastCRT.state != "Aborted"
                [bearOverlap, bullOverlap] = checkBulkyBreak(lastCRT.bulkyHigh, lastCRT.bulkyLow)

                // Bearish break detected
                if bearOverlap and not bullOverlap
                    lastCRT.overlapDirection := "Bear"
                    lastCRT.breakTime := time
                    if entryMode == "FVGs"
                        lastCRT.state := "Waiting For FVG"
                        logStrategy("Bearish break detected - Waiting For FVG")
                    else
                        lastCRT.state := "Waiting For OB"
                        logStrategy("Bearish break detected - Waiting For OB")

                // Bullish break detected
                if bullOverlap and not bearOverlap
                    lastCRT.overlapDirection := "Bull"
                    lastCRT.breakTime := time
                    if entryMode == "FVGs"
                        lastCRT.state := "Waiting For FVG"
                        logStrategy("Bullish break detected - Waiting For FVG")
                    else
                        lastCRT.state := "Waiting For OB"
                        logStrategy("Bullish break detected - Waiting For OB")

                // Conflicting signals - abort
                if bearOverlap and bullOverlap
                    lastCRT.state := "Aborted"
                    logStrategy("Conflicting break signals - CRT aborted")

        // State 3: Waiting For FVG
        if lastCRT.state == "Waiting For FVG"
            isBullish = (lastCRT.overlapDirection == "Bull")
            latestFVG = getLatestFVG(isBullish)

            if not na(latestFVG) and latestFVG.startTime > lastCRT.breakTime
                lastCRT.fvg := createFVG(latestFVG)
                lastCRT.fvg.isActive := true

                if not requireRetracement
                    lastCRT.state := "Enter Position"
                    logStrategy("FVG found - Enter Position (No retracement required)")
                else
                    lastCRT.state := "Waiting For FVG Retracement"
                    logStrategy("FVG found - Waiting For Retracement")

        // State 4: Waiting For FVG Retracement
        if lastCRT.state == "Waiting For FVG Retracement"
            if not na(lastCRT.fvg) and na(lastCRT.fvgEndTime)
                if time > lastCRT.fvg.info.startTime
                    if checkFVGRetracement(lastCRT.fvg.info)
                        lastCRT.state := "Enter Position"
                        logStrategy("FVG retracement detected - Enter Position")

                // Check for FVG invalidation
                if checkFVGInvalidation(lastCRT.fvg.info)
                    lastCRT.fvgEndTime := time
                    lastCRT.state := "Aborted"
                    logStrategy("FVG invalidated - CRT aborted")

        // State 5: Waiting For OB (Order Block mode)
        if lastCRT.state == "Waiting For OB"
            isBullish = (lastCRT.overlapDirection == "Bull")
            latestOB = getLatestOrderBlock(isBullish)

            if not na(latestOB) and latestOB.startTime > lastCRT.breakTime
                lastCRT.ob := createOrderBlock(latestOB)
                lastCRT.ob.isActive := true

                if not requireRetracement
                    lastCRT.state := "Enter Position"
                    logStrategy("Order Block found - Enter Position (No retracement required)")
                else
                    lastCRT.state := "Waiting For OB Retracement"
                    logStrategy("Order Block found - Waiting For Retracement")

        // State 6: Waiting For OB Retracement
        if lastCRT.state == "Waiting For OB Retracement"
            if not na(lastCRT.ob) and not lastCRT.ob.info.breaker
                if time > lastCRT.ob.info.startTime
                    if checkOBRetracement(lastCRT.ob.info)
                        lastCRT.state := "Enter Position"
                        logStrategy("Order Block retracement detected - Enter Position")

                // Check for OB invalidation (becomes breaker)
                if checkOBInvalidation(lastCRT.ob.info)
                    lastCRT.ob.info.breaker := true
                    lastCRT.ob.info.breakTime := time
                    lastCRT.state := "Aborted"
                    logStrategy("Order Block invalidated (became breaker) - CRT aborted")

        // State 7: Enter Position (Core Entry Logic)
        if lastCRT.state == "Enter Position"
            // Validate entry quality, volume confirmation, and time filtering
            qualityValid = useEnhancedRR ? isValidEntry(lastCRT.qualityScore) : true
            volumeValid = isVolumeValid()
            timeValid = isValidTradingTime()
            entryValid = qualityValid and volumeValid and timeValid

            if entryValid
                // Determine entry direction and type
                isLongEntry = (lastCRT.overlapDirection == "Bull")
                entryDirection = isLongEntry ? "Long" : "Short"
                lastCRT.entryType := entryMode + " " + entryDirection

                // Execute strategy entry
                if isLongEntry
                    strategy.entry("CRT Long", strategy.long, qty = defaultQty, comment = lastCRT.entryType)
                    logStrategy("LONG ENTRY EXECUTED - " + lastCRT.entryType)
                else
                    strategy.entry("CRT Short", strategy.short, qty = defaultQty, comment = lastCRT.entryType)
                    logStrategy("SHORT ENTRY EXECUTED - " + lastCRT.entryType)

                // Update CRT state and tracking
                lastCRT.state := "Entry Taken"
                lastCRT.entryTime := time
                lastCRT.entryPrice := close
                totalSignals += 1

                // Update entry tracking variables
                totalEntries += 1
                lastEntryPrice := close
                lastEntryType := lastCRT.entryType
                lastEntryTime := time

                if isLongEntry
                    longEntries += 1
                else
                    shortEntries += 1

                if entryMode == "FVGs"
                    fvgEntries += 1
                else
                    obEntries += 1

                // Enhanced R:R logging
                if useEnhancedRR
                    logStrategy("Entry Details - Quality: " + str.tostring(lastCRT.qualityScore, "#.#") + ", R:R: " + str.tostring(lastCRT.dynamicRR, "#.##"))
                else
                    logStrategy("Entry Details - Standard R:R: " + str.tostring(DynamicRR, "#.##"))

                // Generate enhanced entry alerts
                entryDirection = isLongEntry ? "Long" : "Short"
                sendEntryAlert(entryDirection, lastCRT.entryType, close, lastCRT.qualityScore, lastCRT.dynamicRR)

            else
                // Entry validation failed - log rejection reasons
                rejectionReason = ""
                if not qualityValid
                    rejectionReason += "Quality score too low (" + str.tostring(lastCRT.qualityScore, "#.#") + " < " + str.tostring(minQualityScore, "#.#") + ")"
                if not volumeValid
                    if rejectionReason != ""
                        rejectionReason += " | "
                    rejectionReason += "Volume too low"
                if not timeValid
                    if rejectionReason != ""
                        rejectionReason += " | "
                    rejectionReason += "Time filter active"

                logStrategy("ENTRY REJECTED - " + rejectionReason)
                lastCRT.state := "Aborted"
                rejectedSignals += 1

                // Send error alert for entry rejection
                sendErrorAlert("Entry Rejected", rejectionReason)

        // State 8: Entry Taken (TP/SL Management)
        if lastCRT.state == "Entry Taken"
            // Calculate TP/SL levels if not already set
            if na(lastCRT.slTarget) or na(lastCRT.tpTarget)
                calculateTPSLLevels(lastCRT)

                // Set strategy exit orders
                if not na(lastCRT.slTarget) and not na(lastCRT.tpTarget)
                    if lastCRT.entryType == "Long" or str.contains(lastCRT.entryType, "Long")
                        strategy.exit("CRT Long Exit", "CRT Long", stop = lastCRT.slTarget, limit = lastCRT.tpTarget)
                        logStrategy("Long TP/SL set - TP: " + str.tostring(lastCRT.tpTarget) + ", SL: " + str.tostring(lastCRT.slTarget))
                    else
                        strategy.exit("CRT Short Exit", "CRT Short", stop = lastCRT.slTarget, limit = lastCRT.tpTarget)
                        logStrategy("Short TP/SL set - TP: " + str.tostring(lastCRT.tpTarget) + ", SL: " + str.tostring(lastCRT.slTarget))

            // Monitor for exit execution
            if na(lastCRT.exitPrice) and na(lastCRT.exitTime)
                // Check for Take Profit hit
                if lastCRT.entryType == "Long" or str.contains(lastCRT.entryType, "Long")
                    if high >= lastCRT.tpTarget
                        lastCRT.exitPrice := lastCRT.tpTarget
                        lastCRT.exitTime := time
                        lastCRT.state := "Take Profit"

                        // Update exit tracking
                        totalExits += 1
                        takeProfitExits += 1
                        lastExitPrice := lastCRT.tpTarget
                        lastExitType := "Take Profit"
                        lastExitTime := time

                        // Calculate and track R:R
                        actualRR = calculateActualRR(lastCRT)
                        if not na(actualRR)
                            totalRR += actualRR
                            avgRR := totalRR / totalExits

                        logStrategy("TAKE PROFIT HIT - Long exit at " + str.tostring(lastCRT.tpTarget) + " | R:R: " + str.tostring(actualRR, "#.##"))
                        sendExitAlert("Long", "Take Profit", lastCRT.tpTarget, actualRR)

                    // Check for Stop Loss hit
                    else if low <= lastCRT.slTarget
                        lastCRT.exitPrice := lastCRT.slTarget
                        lastCRT.exitTime := time
                        lastCRT.state := "Stop Loss"

                        // Update exit tracking
                        totalExits += 1
                        stopLossExits += 1
                        lastExitPrice := lastCRT.slTarget
                        lastExitType := "Stop Loss"
                        lastExitTime := time

                        // Calculate and track R:R (negative for losses)
                        actualRR = calculateActualRR(lastCRT)
                        if not na(actualRR)
                            totalRR += actualRR
                            avgRR := totalRR / totalExits

                        logStrategy("STOP LOSS HIT - Long exit at " + str.tostring(lastCRT.slTarget) + " | R:R: " + str.tostring(actualRR, "#.##"))
                        sendExitAlert("Long", "Stop Loss", lastCRT.slTarget, actualRR)

                else // Short position
                    if low <= lastCRT.tpTarget
                        lastCRT.exitPrice := lastCRT.tpTarget
                        lastCRT.exitTime := time
                        lastCRT.state := "Take Profit"

                        // Update exit tracking
                        totalExits += 1
                        takeProfitExits += 1
                        lastExitPrice := lastCRT.tpTarget
                        lastExitType := "Take Profit"
                        lastExitTime := time

                        // Calculate and track R:R
                        actualRR = calculateActualRR(lastCRT)
                        if not na(actualRR)
                            totalRR += actualRR
                            avgRR := totalRR / totalExits

                        logStrategy("TAKE PROFIT HIT - Short exit at " + str.tostring(lastCRT.tpTarget) + " | R:R: " + str.tostring(actualRR, "#.##"))
                        sendExitAlert("Short", "Take Profit", lastCRT.tpTarget, actualRR)

                    // Check for Stop Loss hit
                    else if high >= lastCRT.slTarget
                        lastCRT.exitPrice := lastCRT.slTarget
                        lastCRT.exitTime := time
                        lastCRT.state := "Stop Loss"

                        // Update exit tracking
                        totalExits += 1
                        stopLossExits += 1
                        lastExitPrice := lastCRT.slTarget
                        lastExitType := "Stop Loss"
                        lastExitTime := time

                        // Calculate and track R:R (negative for losses)
                        actualRR = calculateActualRR(lastCRT)
                        if not na(actualRR)
                            totalRR += actualRR
                            avgRR := totalRR / totalExits

                        logStrategy("STOP LOSS HIT - Short exit at " + str.tostring(lastCRT.slTarget) + " | R:R: " + str.tostring(actualRR, "#.##"))
                        sendExitAlert("Short", "Stop Loss", lastCRT.slTarget, actualRR)

//#endregion

//#region Entry Validation Functions - Phase 7

// Validate if entry conditions are met
validateEntryConditions(CRT crt) =>
    valid = true
    reason = ""

    // Check CRT state
    if crt.state != "Enter Position"
        valid := false
        reason += "Invalid CRT state"

    // Check quality score if enhanced R:R is enabled
    if useEnhancedRR and not na(crt.qualityScore)
        if crt.qualityScore < minQualityScore
            valid := false
            reason += (reason == "" ? "" : " | ") + "Quality too low"

    // Check volume confirmation
    if requireVolumeConfirmation and not isVolumeValid()
        valid := false
        reason += (reason == "" ? "" : " | ") + "Volume too low"

    // Check time filtering
    if useTimeFiltering and not isValidTradingTime()
        valid := false
        reason += (reason == "" ? "" : " | ") + "Time filter active"

    [valid, reason]

// Get entry direction from CRT
getEntryDirection(CRT crt) =>
    direction = "None"
    if not na(crt.overlapDirection)
        direction := crt.overlapDirection == "Bull" ? "Long" : "Short"
    direction

// Calculate entry quantity based on strategy settings
calculateEntryQuantity() =>
    // Use default quantity from strategy settings
    // This can be enhanced in future phases for dynamic position sizing
    defaultQty

// Validate position size
validatePositionSize(qty) =>
    valid = true
    if qty <= 0 or qty > 100
        valid := false
    valid

//#endregion

//#region TP/SL Management System - Phase 8

// Calculate TP/SL levels based on method and enhanced R:R
calculateTPSLLevels(CRT crt) =>
    if na(crt.entryPrice)
        logStrategy("Error: Cannot calculate TP/SL - Entry price is na")
        return

    isLongPosition = (crt.entryType == "Long" or str.contains(crt.entryType, "Long"))

    if tpslMethod == "Fixed"
        // Fixed percentage TP/SL calculation
        if isLongPosition
            crt.slTarget := crt.entryPrice * (1 - slPercent / 100.0)
            crt.tpTarget := crt.entryPrice * (1 + tpPercent / 100.0)
        else
            crt.slTarget := crt.entryPrice * (1 + slPercent / 100.0)
            crt.tpTarget := crt.entryPrice * (1 - tpPercent / 100.0)

        logStrategy("Fixed TP/SL calculated - Method: " + tpslMethod + ", TP%: " + str.tostring(tpPercent) + ", SL%: " + str.tostring(slPercent))

    else if tpslMethod == "Dynamic"
        // Dynamic ATR-based TP/SL calculation
        if isLongPosition
            crt.slTarget := crt.entryPrice - atr * slATRMult
            riskDistance = math.abs(crt.entryPrice - crt.slTarget)
            enhancedRR = useEnhancedRR ? crt.dynamicRR : DynamicRR
            crt.tpTarget := crt.entryPrice + (riskDistance * enhancedRR)
        else
            crt.slTarget := crt.entryPrice + atr * slATRMult
            riskDistance = math.abs(crt.entryPrice - crt.slTarget)
            enhancedRR = useEnhancedRR ? crt.dynamicRR : DynamicRR
            crt.tpTarget := crt.entryPrice - (riskDistance * enhancedRR)

        logStrategy("Dynamic TP/SL calculated - Method: " + tpslMethod + ", ATR Mult: " + str.tostring(slATRMult) + ", R:R: " + str.tostring(enhancedRR))

// Get SL ATR multiplier based on risk level
getSLATRMultiplier() =>
    mult = riskAmount == "Highest" ? 10.0 : riskAmount == "High" ? 8.0 : riskAmount == "Normal" ? 6.5 : riskAmount == "Low" ? 5.0 : riskAmount == "Lowest" ? 3.0 : 6.5
    mult

// SL ATR multiplier already calculated above (line 65)

// Calculate actual R:R ratio achieved
calculateActualRR(CRT crt) =>
    float actualRR = na
    if not na(crt.entryPrice) and not na(crt.exitPrice) and not na(crt.slTarget)
        riskDistance = math.abs(crt.entryPrice - crt.slTarget)
        rewardDistance = math.abs(crt.exitPrice - crt.entryPrice)
        if riskDistance > 0
            actualRR := rewardDistance / riskDistance
    actualRR

// Validate TP/SL levels
validateTPSLLevels(CRT crt) =>
    valid = true
    reason = ""

    if na(crt.slTarget) or na(crt.tpTarget)
        valid := false
        reason += "TP/SL levels not calculated"

    // Check if TP/SL are on correct side of entry
    isLongPosition = (crt.entryType == "Long" or str.contains(crt.entryType, "Long"))
    if not na(crt.slTarget) and not na(crt.tpTarget) and not na(crt.entryPrice)
        if isLongPosition
            if crt.slTarget >= crt.entryPrice
                valid := false
                reason += (reason == "" ? "" : " | ") + "Long SL above entry"
            if crt.tpTarget <= crt.entryPrice
                valid := false
                reason += (reason == "" ? "" : " | ") + "Long TP below entry"
        else
            if crt.slTarget <= crt.entryPrice
                valid := false
                reason += (reason == "" ? "" : " | ") + "Short SL below entry"
            if crt.tpTarget >= crt.entryPrice
                valid := false
                reason += (reason == "" ? "" : " | ") + "Short TP above entry"

    [valid, reason]

//#endregion

//#region Enhanced Alert System - Phase 9

// Alert tracking variables
var int totalAlerts = 0
var int entryAlerts = 0
var int exitAlerts = 0
var int errorAlerts = 0
var int statusAlerts = 0
var string lastAlertMessage = na
var int lastAlertTime = na

// Generate base alert information
getBaseAlertInfo() =>
    baseInfo = ""
    if includeSymbol
        baseInfo += syminfo.ticker
    if includeTimeframe
        baseInfo += (baseInfo == "" ? "" : " ") + timeframe.period
    if baseInfo != ""
        baseInfo := "[" + baseInfo + "] "
    alertPrefix + " " + baseInfo

// Format alert message based on settings
formatAlertMessage(alertType, message, additionalInfo = "") =>
    baseInfo = getBaseAlertInfo()
    formattedMessage = ""

    if enableWebhookFormat
        // JSON-like format for webhook integration
        formattedMessage := "{"
        formattedMessage += "\"strategy\":\"" + alertPrefix + "\","
        formattedMessage += "\"symbol\":\"" + syminfo.ticker + "\","
        formattedMessage += "\"timeframe\":\"" + timeframe.period + "\","
        formattedMessage += "\"type\":\"" + alertType + "\","
        formattedMessage += "\"message\":\"" + message + "\","
        formattedMessage += "\"timestamp\":\"" + str.tostring(time) + "\""
        if additionalInfo != ""
            formattedMessage += "," + additionalInfo
        formattedMessage += "}"
    else
        // Standard format
        formattedMessage := baseInfo + alertType + ": " + message
        if enableDetailedAlerts and additionalInfo != ""
            formattedMessage += " | " + additionalInfo

    formattedMessage

// Send entry alert
sendEntryAlert(direction, entryType, price, qualityScore = na, dynamicRR = na) =>
    if (direction == "Long" and buyAlertEnabled) or (direction == "Short" and sellAlertEnabled)
        message = direction + " Entry at " + str.tostring(price, "#.#####")
        additionalInfo = ""

        if enableDetailedAlerts
            additionalInfo += "\"entry_type\":\"" + entryType + "\""
            if not na(qualityScore)
                additionalInfo += ",\"quality_score\":" + str.tostring(qualityScore, "#.#")
            if not na(dynamicRR)
                additionalInfo += ",\"risk_reward\":" + str.tostring(dynamicRR, "#.##")
            additionalInfo += ",\"entry_mode\":\"" + entryMode + "\""

        alertMessage = formatAlertMessage("ENTRY", message, additionalInfo)
        alert(alertMessage, alert.freq_once_per_bar)

        // Update tracking
        totalAlerts += 1
        entryAlerts += 1
        lastAlertMessage := alertMessage
        lastAlertTime := time

        logStrategy("Entry alert sent: " + alertMessage)

// Send exit alert
sendExitAlert(direction, exitType, price, actualRR = na) =>
    if (exitType == "Take Profit" and tpAlertEnabled) or (exitType == "Stop Loss" and slAlertEnabled)
        message = direction + " " + exitType + " at " + str.tostring(price, "#.#####")
        additionalInfo = ""

        if enableDetailedAlerts
            additionalInfo += "\"exit_type\":\"" + exitType + "\""
            if not na(actualRR)
                additionalInfo += ",\"actual_rr\":" + str.tostring(actualRR, "#.##")
            additionalInfo += ",\"direction\":\"" + direction + "\""

        alertMessage = formatAlertMessage("EXIT", message, additionalInfo)
        alert(alertMessage, alert.freq_once_per_bar)

        // Update tracking
        totalAlerts += 1
        exitAlerts += 1
        lastAlertMessage := alertMessage
        lastAlertTime := time

        logStrategy("Exit alert sent: " + alertMessage)

// Send error alert
sendErrorAlert(errorType, reason) =>
    if enableErrorAlerts
        message = errorType + " - " + reason
        additionalInfo = ""

        if enableDetailedAlerts
            additionalInfo += "\"error_type\":\"" + errorType + "\""
            additionalInfo += ",\"reason\":\"" + reason + "\""

        alertMessage = formatAlertMessage("ERROR", message, additionalInfo)
        alert(alertMessage, alert.freq_once_per_bar)

        // Update tracking
        totalAlerts += 1
        errorAlerts += 1
        lastAlertMessage := alertMessage
        lastAlertTime := time

        logStrategy("Error alert sent: " + alertMessage)

// Send status alert
sendStatusAlert(statusType, message, details = "") =>
    if enableStatusAlerts
        additionalInfo = ""

        if enableDetailedAlerts and details != ""
            additionalInfo += "\"status_type\":\"" + statusType + "\""
            additionalInfo += ",\"details\":\"" + details + "\""

        alertMessage = formatAlertMessage("STATUS", message, additionalInfo)
        alert(alertMessage, alert.freq_once_per_bar)

        // Update tracking
        totalAlerts += 1
        statusAlerts += 1
        lastAlertMessage := alertMessage
        lastAlertTime := time

        logStrategy("Status alert sent: " + alertMessage)

// Validate alert settings
validateAlertSettings() =>
    valid = true
    issues = ""

    if not buyAlertEnabled and not sellAlertEnabled and not tpAlertEnabled and not slAlertEnabled
        valid := false
        issues += "No alerts enabled"

    if enableWebhookFormat and not enableDetailedAlerts
        issues += (issues == "" ? "" : " | ") + "Webhook format recommended with detailed alerts"

    [valid, issues]

//#endregion

//#region Comprehensive Strategy Validation - Phase 10

// Strategy validation tracking variables
var int validationErrors = 0
var int validationWarnings = 0
var string lastValidationError = na
var string lastValidationWarning = na
var bool systemHealthy = true

// Comprehensive strategy validation
validateStrategySystem() =>
    errors = 0
    warnings = 0
    errorMessages = ""
    warningMessages = ""

    // 1. Core System Validation
    if not isHTFDataValid()
        errors += 1
        errorMessages += "HTF data invalid | "

    // 2. Array System Validation
    if allFVGList.size() > 100
        warnings += 1
        warningMessages += "FVG array size excessive (" + str.tostring(allFVGList.size()) + ") | "

    if allOrderBlockList.size() > 50
        warnings += 1
        warningMessages += "OB array size excessive (" + str.tostring(allOrderBlockList.size()) + ") | "

    if crtList.size() > 20
        warnings += 1
        warningMessages += "CRT array size excessive (" + str.tostring(crtList.size()) + ") | "

    // 3. Performance Validation
    if bar_index > 1000 and totalSignals == 0
        warnings += 1
        warningMessages += "No signals generated after 1000 bars | "

    if totalSignals > 0 and rejectedSignals / totalSignals > 0.8
        warnings += 1
        warningMessages += "High rejection rate (" + str.tostring(rejectedSignals / totalSignals * 100, "#.#") + "%) | "

    // 4. Alert System Validation
    [alertValid, alertIssues] = validateAlertSettings()
    if not alertValid
        warnings += 1
        warningMessages += "Alert configuration issues: " + alertIssues + " | "

    // 5. Entry/Exit Balance Validation
    if totalEntries > 0 and totalExits == 0
        warnings += 1
        warningMessages += "Entries without exits detected | "

    if totalEntries > 0 and totalExits > totalEntries
        errors += 1
        errorMessages += "More exits than entries (data integrity issue) | "

    // 6. R:R System Validation
    if useEnhancedRR and totalExits > 5 and not na(avgRR) and avgRR < -2.0
        warnings += 1
        warningMessages += "Poor average R:R performance (" + str.tostring(avgRR, "#.##") + ") | "

    // Update tracking variables
    validationErrors := errors
    validationWarnings := warnings
    if errorMessages != ""
        lastValidationError := errorMessages
    if warningMessages != ""
        lastValidationWarning := warningMessages

    systemHealthy := (errors == 0)

    [errors, warnings, errorMessages, warningMessages]

// Validate data integrity across all systems
validateDataIntegrity() =>
    issues = 0
    issueDetails = ""

    // Check for na values in critical data
    if not na(lastCRT) and na(lastCRT.startTime)
        issues += 1
        issueDetails += "CRT missing start time | "

    // Check FVG data consistency
    if allFVGList.size() > 0
        for i = 0 to math.min(allFVGList.size() - 1, 9)  // Check first 10
            currentFVG = allFVGList.get(i)
            if na(currentFVG.info.startTime) or na(currentFVG.info.max) or na(currentFVG.info.min)
                issues += 1
                issueDetails += "FVG data integrity issue | "
                break

    // Check Order Block data consistency
    if allOrderBlockList.size() > 0
        for i = 0 to math.min(allOrderBlockList.size() - 1, 9)  // Check first 10
            currentOB = allOrderBlockList.get(i)
            if na(currentOB.info.startTime) or na(currentOB.info.top) or na(currentOB.info.bottom)
                issues += 1
                issueDetails += "OB data integrity issue | "
                break

    // Check entry/exit data consistency
    if not na(lastCRT) and not na(lastCRT.entryPrice) and not na(lastCRT.exitPrice)
        if lastCRT.entryPrice <= 0 or lastCRT.exitPrice <= 0
            issues += 1
            issueDetails += "Invalid entry/exit prices | "

    [issues, issueDetails]

// Validate performance metrics
validatePerformanceMetrics() =>
    issues = 0
    details = ""

    // Check for reasonable performance bounds
    if totalEntries > 1000
        issues += 1
        details += "Excessive entries (" + str.tostring(totalEntries) + ") | "

    if totalAlerts > 5000
        issues += 1
        details += "Excessive alerts (" + str.tostring(totalAlerts) + ") | "

    // Check R:R reasonableness
    if not na(avgRR) and (avgRR > 10.0 or avgRR < -10.0)
        issues += 1
        details += "Unrealistic average R:R (" + str.tostring(avgRR, "#.##") + ") | "

    // Check win rate if we have enough data
    if totalExits >= 10
        winRate = takeProfitExits / totalExits
        if winRate > 0.95 or winRate < 0.05
            issues += 1
            details += "Unrealistic win rate (" + str.tostring(winRate * 100, "#.#") + "%) | "

    [issues, details]

// Validate system resource usage
validateResourceUsage() =>
    issues = 0
    details = ""

    // Check array sizes for memory efficiency
    totalArrayElements = allFVGList.size() + allOrderBlockList.size() + crtList.size()
    if totalArrayElements > 200
        issues += 1
        details += "High memory usage (" + str.tostring(totalArrayElements) + " elements) | "

    // Check for potential infinite loops or excessive calculations
    if bar_index > 100 and totalBulkyCandles == 0
        issues += 1
        details += "No HTF detection after 100 bars | "

    [issues, details]

// Production readiness checklist
validateProductionReadiness() =>
    ready = true
    issues = ""

    // Essential systems check
    if not isHTFDataValid()
        ready := false
        issues += "HTF system not operational | "

    // Alert system check
    if not buyAlertEnabled and not sellAlertEnabled
        ready := false
        issues += "No entry alerts enabled | "

    // Entry/Exit system check
    if totalEntries == 0 and bar_index > 500
        ready := false
        issues += "No entries after 500 bars | "

    // Data integrity check
    [dataIssues, dataDetails] = validateDataIntegrity()
    if dataIssues > 0
        ready := false
        issues += "Data integrity issues | "

    // Performance check
    [perfIssues, perfDetails] = validatePerformanceMetrics()
    if perfIssues > 3
        ready := false
        issues += "Performance issues | "

    [ready, issues]

// Generate comprehensive validation report
generateValidationReport() =>
    report = "=== CRT Strategy Validation Report ===\n"

    // System validation
    [sysErrors, sysWarnings, errorMsg, warningMsg] = validateStrategySystem()
    report += "System Health: " + (systemHealthy ? "HEALTHY" : "ISSUES DETECTED") + "\n"
    report += "Errors: " + str.tostring(sysErrors) + " | Warnings: " + str.tostring(sysWarnings) + "\n"

    // Data integrity
    [dataIssues, dataDetails] = validateDataIntegrity()
    report += "Data Integrity: " + (dataIssues == 0 ? "CLEAN" : str.tostring(dataIssues) + " issues") + "\n"

    // Performance metrics
    [perfIssues, perfDetails] = validatePerformanceMetrics()
    report += "Performance: " + (perfIssues == 0 ? "OPTIMAL" : str.tostring(perfIssues) + " issues") + "\n"

    // Production readiness
    [prodReady, prodIssues] = validateProductionReadiness()
    report += "Production Ready: " + (prodReady ? "YES" : "NO - " + prodIssues) + "\n"

    // Statistics summary
    report += "=== Statistics ===\n"
    report += "Total Entries: " + str.tostring(totalEntries) + " | Total Exits: " + str.tostring(totalExits) + "\n"
    report += "Total Alerts: " + str.tostring(totalAlerts) + " | Avg R:R: " + str.tostring(avgRR, "#.##") + "\n"
    report += "Active Arrays: FVG(" + str.tostring(activeFVGs) + ") OB(" + str.tostring(activeOrderBlocks) + ") CRT(" + str.tostring(activeCRTs) + ")\n"

    report

//#endregion

// Perform comprehensive validation checks
if bar_index % 250 == 0 or barstate.islast  // Every 250 bars and on last bar
    [sysErrors, sysWarnings, errorMsg, warningMsg] = validateStrategySystem()

    if sysErrors > 0
        logStrategy("VALIDATION ERRORS: " + errorMsg)
        if enableErrorAlerts
            sendErrorAlert("System Validation", errorMsg)

    if sysWarnings > 0
        logStrategy("VALIDATION WARNINGS: " + warningMsg)

// Update strategy status and perform final validation
if barstate.islast
    // Perform comprehensive final validation
    [prodReady, prodIssues] = validateProductionReadiness()

    if prodReady
        strategyStatus := "Production Ready"
        logStrategy("=== STRATEGY VALIDATION COMPLETE ===")
        logStrategy("Status: PRODUCTION READY")

        // Send success status alert
        if enableStatusAlerts
            sendStatusAlert("Validation Complete", "Strategy passed all validation checks and is production ready", "")
    else
        strategyStatus := "Validation Issues"
        logStrategy("=== STRATEGY VALIDATION ISSUES ===")
        logStrategy("Issues: " + prodIssues)

        // Send validation failure alert
        if enableErrorAlerts
            sendErrorAlert("Validation Failed", prodIssues)

    // Generate and log comprehensive validation report
    validationReport = generateValidationReport()
    logStrategy(validationReport)

    // Validate alert settings
    [alertValid, alertIssues] = validateAlertSettings()
    if not alertValid
        logStrategy("Alert validation issues: " + alertIssues)
        if enableErrorAlerts
            sendErrorAlert("Alert Configuration", alertIssues)

    logStrategy("=== CRT STRATEGY FINAL STATUS ===")
    logStrategy("Strategy Status: " + strategyStatus)
    logStrategy("System Health: " + (systemHealthy ? "HEALTHY" : "ISSUES DETECTED"))
    logStrategy("Validation: " + str.tostring(validationErrors) + " errors, " + str.tostring(validationWarnings) + " warnings")
    logStrategy("")
    logStrategy("=== DETECTION SYSTEMS ===")
    logStrategy("Active FVGs: " + str.tostring(activeFVGs))
    logStrategy("Total FVGs: " + str.tostring(totalFVGs) + " (Bull: " + str.tostring(bullishFVGs) + ", Bear: " + str.tostring(bearishFVGs) + ")")
    logStrategy("Active Order Blocks: " + str.tostring(activeOrderBlocks))
    logStrategy("Total Order Blocks: " + str.tostring(totalOrderBlocks) + " (Bull: " + str.tostring(bullishOrderBlocks) + ", Bear: " + str.tostring(bearishOrderBlocks) + ")")
    logStrategy("Active CRTs: " + str.tostring(activeCRTs))
    logStrategy("Total Bulky Candles: " + str.tostring(totalBulkyCandles))
    logStrategy("Valid Bulky Candles: " + str.tostring(validBulkyCandles))
    logStrategy("")
    logStrategy("=== TRADING PERFORMANCE ===")
    logStrategy("Total Entries: " + str.tostring(totalEntries) + " (Long: " + str.tostring(longEntries) + ", Short: " + str.tostring(shortEntries) + ")")
    logStrategy("Entry Types: FVG: " + str.tostring(fvgEntries) + ", OB: " + str.tostring(obEntries))
    logStrategy("Total Exits: " + str.tostring(totalExits) + " (TP: " + str.tostring(takeProfitExits) + ", SL: " + str.tostring(stopLossExits) + ")")
    logStrategy("Average R:R: " + str.tostring(avgRR, "#.##") + " | TP/SL Method: " + tpslMethod)
    if totalExits > 0
        winRate = takeProfitExits / totalExits * 100
        logStrategy("Win Rate: " + str.tostring(winRate, "#.#") + "%")
    logStrategy("Total signals generated: " + str.tostring(totalSignals))
    logStrategy("Rejected signals: " + str.tostring(rejectedSignals))
    if totalSignals > 0
        rejectionRate = rejectedSignals / totalSignals * 100
        logStrategy("Rejection Rate: " + str.tostring(rejectionRate, "#.#") + "%")
    logStrategy("")
    logStrategy("=== ALERT SYSTEM ===")
    logStrategy("Total Alerts: " + str.tostring(totalAlerts) + " (Entry: " + str.tostring(entryAlerts) + ", Exit: " + str.tostring(exitAlerts) + ", Error: " + str.tostring(errorAlerts) + ")")
    logStrategy("Alert Format: " + (enableWebhookFormat ? "Webhook" : "Standard") + " | Detailed: " + str.tostring(enableDetailedAlerts))
    logStrategy("Alert Settings: Buy:" + str.tostring(buyAlertEnabled) + " Sell:" + str.tostring(sellAlertEnabled) + " TP:" + str.tostring(tpAlertEnabled) + " SL:" + str.tostring(slAlertEnabled))

    // Send status alert for strategy initialization
    if enableStatusAlerts
        statusDetails = "Entries: " + str.tostring(totalEntries) + ", Exits: " + str.tostring(totalExits) + ", Avg R:R: " + str.tostring(avgRR, "#.##")
        sendStatusAlert("Strategy Ready", "CRT Strategy fully initialized and operational", statusDetails)

// Display strategy information (for debugging)
if DEBUG and barstate.islast
    var table infoTable = table.new(position.top_right, 2, 24, bgcolor = color.white, border_width = 1)
    table.cell(infoTable, 0, 0, "CRT Strategy Status", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 0, strategyStatus, text_color = systemHealthy ? color.green : color.red, text_size = size.small)
    table.cell(infoTable, 0, 1, "System Health", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 1, systemHealthy ? "HEALTHY" : "ISSUES", text_color = systemHealthy ? color.green : color.red, text_size = size.small)
    table.cell(infoTable, 0, 2, "Validation", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 2, str.tostring(validationErrors) + "E/" + str.tostring(validationWarnings) + "W", text_color = validationErrors > 0 ? color.red : (validationWarnings > 0 ? color.orange : color.green), text_size = size.small)
    table.cell(infoTable, 0, 3, "Higher TF", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 3, higherTF, text_color = color.black, text_size = size.small)
    table.cell(infoTable, 0, 4, "HTF Valid", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 4, str.tostring(isHTFDataValid()), text_color = isHTFDataValid() ? color.green : color.red, text_size = size.small)
    table.cell(infoTable, 0, 5, "HTF Candle Size", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 5, str.tostring(getHTFCandleSize(), "#.##"), text_color = color.black, text_size = size.small)
    table.cell(infoTable, 0, 6, "Entry Mode", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 6, entryMode, text_color = color.black, text_size = size.small)
    table.cell(infoTable, 0, 7, "Swing Length", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 7, str.tostring(swingLength), text_color = color.black, text_size = size.small)
    table.cell(infoTable, 0, 8, "FVG Sensitivity", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 8, fvgSensitivityText, text_color = color.black, text_size = size.small)
    table.cell(infoTable, 0, 9, "TP/SL Method", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 9, tpslMethod, text_color = color.black, text_size = size.small)
    table.cell(infoTable, 0, 10, "Risk Level", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 10, riskAmount, text_color = color.black, text_size = size.small)
    table.cell(infoTable, 0, 11, "Quality Score", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 11, str.tostring(minQualityScore), text_color = color.black, text_size = size.small)
    table.cell(infoTable, 0, 12, "Active FVGs", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 12, str.tostring(activeFVGs), text_color = color.green, text_size = size.small)
    table.cell(infoTable, 0, 13, "Total FVGs", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 13, str.tostring(bullishFVGs) + "B/" + str.tostring(bearishFVGs) + "B", text_color = color.orange, text_size = size.small)
    table.cell(infoTable, 0, 14, "Active OBs", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 14, str.tostring(activeOrderBlocks), text_color = color.green, text_size = size.small)
    table.cell(infoTable, 0, 15, "Total OBs", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 15, str.tostring(bullishOrderBlocks) + "B/" + str.tostring(bearishOrderBlocks) + "B", text_color = color.orange, text_size = size.small)
    table.cell(infoTable, 0, 16, "Total Entries", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 16, str.tostring(longEntries) + "L/" + str.tostring(shortEntries) + "S", text_color = color.lime, text_size = size.small)
    table.cell(infoTable, 0, 17, "Entry Types", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 17, str.tostring(fvgEntries) + "FVG/" + str.tostring(obEntries) + "OB", text_color = color.lime, text_size = size.small)
    table.cell(infoTable, 0, 18, "Total Exits", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 18, str.tostring(takeProfitExits) + "TP/" + str.tostring(stopLossExits) + "SL", text_color = color.yellow, text_size = size.small)
    table.cell(infoTable, 0, 19, "Average R:R", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 19, str.tostring(avgRR, "#.##"), text_color = color.yellow, text_size = size.small)
    table.cell(infoTable, 0, 20, "Total Alerts", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 20, str.tostring(entryAlerts) + "E/" + str.tostring(exitAlerts) + "X/" + str.tostring(errorAlerts) + "R", text_color = color.aqua, text_size = size.small)
    table.cell(infoTable, 0, 21, "Alert Format", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 21, enableWebhookFormat ? "Webhook" : "Standard", text_color = color.aqua, text_size = size.small)
    table.cell(infoTable, 0, 22, "Bulky Candles", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 22, str.tostring(validBulkyCandles) + "/" + str.tostring(totalBulkyCandles), text_color = color.blue, text_size = size.small)
    table.cell(infoTable, 0, 23, "CRT State", text_color = color.black, text_size = size.small)
    table.cell(infoTable, 1, 23, na(lastCRT) ? "None" : lastCRT.state, text_color = color.purple, text_size = size.small)
