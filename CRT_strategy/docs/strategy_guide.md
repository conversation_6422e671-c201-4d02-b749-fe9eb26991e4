# CRT Strategy User Guide

## Overview
The CRT (Candle Range Theory) Strategy is a TradingView strategy converted from the highly successful CRT indicator. It maintains 100% identical logic while providing native strategy functionality for automated trading and broker integration.

## Strategy Concept

### Core Theory
The CRT strategy is based on the concept that significant price movements often occur when:
1. **Higher Timeframe (HTF) Candles** show substantial range (bulky candles)
2. **Fair Value Gaps (FVGs)** or **Order Blocks (OBs)** form during these movements
3. **Price retraces** to these zones, providing optimal entry opportunities
4. **Quality and volume confirmation** validates the setup

### Entry Logic Flow
```
HTF Bulky Candle Detection
         ↓
FVG/Order Block Formation
         ↓
Quality Score Calculation
         ↓
Volume Confirmation
         ↓
Time Filtering
         ↓
Entry Execution
```

## Strategy Features

### ✅ Included Features
- **Higher Timeframe Analysis**: Multi-timeframe candle range analysis
- **Dual Entry Modes**: FVGs and Order Blocks
- **Quality Scoring System**: 1-5 scale setup quality assessment
- **Volume Confirmation**: SMA-based volume validation
- **Time Filtering**: Comprehensive session and time-based filtering
- **Dynamic TP/SL**: ATR-based risk management
- **Fixed TP/SL**: Percentage-based risk management
- **Enhanced R:R System**: Quality-based risk-reward optimization
- **Alert System**: Webhook-compatible alerts for broker integration

### ❌ Excluded Features (From Original Indicator)
- **Partial Profit Taking**: Removed as requested
- **Trailing Stop Loss**: Removed as requested
- **Visual Elements**: No boxes, lines, or visual markers
- **Backtesting Dashboard**: Strategy has built-in backtesting

## Parameter Configuration

### General Configuration
| Parameter | Default | Description |
|-----------|---------|-------------|
| **Higher Timeframe** | 240 (4H) | HTF for bulky candle detection |
| **HTF Candle Size** | Big | Candle size requirement (Big/Normal/Small) |
| **Entry Mode** | FVGs | Entry method (FVGs/Order Blocks) |
| **Require Retracement** | false | Require retracement for entry |

### Fair Value Gaps
| Parameter | Default | Description |
|-----------|---------|-------------|
| **FVG Detection Sensitivity** | High | Sensitivity level (All/Extreme/High/Normal/Low) |
| **Swing Length** | 10 | Swing length for Order Block detection |

### TP/SL Configuration
| Parameter | Default | Description |
|-----------|---------|-------------|
| **TP/SL Method** | Dynamic | Dynamic (ATR-based) or Fixed (percentage) |
| **Dynamic Risk** | High | Risk level for ATR multiplier |
| **Fixed Take Profit %** | 0.3% | Fixed TP percentage |
| **Fixed Stop Loss %** | 0.4% | Fixed SL percentage |

### Enhanced R:R System
| Parameter | Default | Description |
|-----------|---------|-------------|
| **Use Enhanced R:R** | false | Enable quality-based R:R |
| **Minimum R:R Ratio** | 1.5 | Minimum risk-reward ratio |
| **Maximum R:R Ratio** | 2.5 | Maximum risk-reward ratio |
| **Require Volume Confirmation** | false | Require above-average volume |
| **Minimum Quality Score** | 1.0 | Minimum setup quality (1-5 scale) |

### Time Filtering
| Parameter | Default | Description |
|-----------|---------|-------------|
| **Enable Time Filtering** | false | Enable time-based trade filtering |
| **Avoid Weekends** | false | Skip Saturday/Sunday trades |
| **Avoid Lunch Hours** | false | Skip 12:00-13:00 period |
| **Trade London Only** | false | London session only (08:00-16:00) |
| **Trade New York Only** | false | New York session only (13:00-21:00) |
| **Avoid Mondays** | false | Skip Monday trades |
| **Avoid Fridays** | false | Skip Friday trades |

## Recommended Settings

### Conservative Setup (Lower Risk)
```
HTF Candle Size: Normal
Dynamic Risk: Low (5x ATR)
Minimum Quality Score: 3.0
Require Volume Confirmation: true
Enable Time Filtering: true
Avoid Lunch Hours: true
Avoid Weekends: true
```

### Aggressive Setup (Higher Risk/Reward)
```
HTF Candle Size: Big
Dynamic Risk: High (8x ATR)
Minimum Quality Score: 1.0
Require Volume Confirmation: false
Enable Time Filtering: false
Use Enhanced R:R: true
Maximum R:R Ratio: 3.0
```

### Scalping Setup (High Frequency)
```
HTF Candle Size: Small
Entry Mode: FVGs
FVG Sensitivity: All
Dynamic Risk: Lowest (3x ATR)
Minimum Quality Score: 1.0
Time Filtering: Custom (9:00-17:00)
```

## Best Practices

### Symbol Selection
- **Forex Majors**: EURUSD, GBPUSD, USDJPY (high liquidity)
- **Crypto**: BTCUSD, ETHUSD (high volatility)
- **Indices**: SPX500, NAS100 (trending markets)

### Timeframe Recommendations
| Current TF | Recommended HTF | Use Case |
|------------|-----------------|----------|
| 1M | 15M or 30M | Scalping |
| 5M | 1H or 4H | Day Trading |
| 15M | 4H or Daily | Swing Trading |
| 1H | Daily or Weekly | Position Trading |

### Risk Management
- **Position Size**: Use 1-2% risk per trade
- **Maximum Drawdown**: Set stop at 10-15% account drawdown
- **Correlation**: Avoid multiple correlated pairs
- **News Events**: Avoid trading during high-impact news

## Alert Configuration

### Webhook Setup for Broker Integration
```json
{
  "action": "{{strategy.order.action}}",
  "symbol": "{{ticker}}",
  "quantity": "{{strategy.order.contracts}}",
  "price": "{{strategy.order.price}}",
  "type": "{{strategy.order.comment}}",
  "timestamp": "{{time}}"
}
```

### Alert Message Templates
- **Entry Alert**: "CRT {{strategy.order.action}} signal on {{ticker}} at {{strategy.order.price}}"
- **Exit Alert**: "CRT {{strategy.order.action}} exit on {{ticker}} at {{strategy.order.price}}"

## Performance Optimization

### Strategy Settings
- **Initial Capital**: $100,000 (adjust based on account size)
- **Order Size**: 100% of equity (for percentage-based position sizing)
- **Commission**: 0.1% (adjust based on broker)
- **Slippage**: 3 ticks (adjust based on market conditions)
- **Pyramiding**: 1 (no position scaling)

### Backtesting Tips
- **Test Period**: Minimum 1 year of data
- **Multiple Symbols**: Test across different asset classes
- **Parameter Sensitivity**: Test with various parameter combinations
- **Walk-Forward Analysis**: Validate on out-of-sample data

## Troubleshooting

### Common Issues
| Issue | Cause | Solution |
|-------|-------|----------|
| No trades generated | HTF too high or quality score too strict | Lower HTF or reduce quality threshold |
| Too many trades | Sensitivity too high or no filtering | Increase quality score or enable time filtering |
| Poor performance | Wrong timeframe combination | Adjust HTF relative to current timeframe |
| Alerts not working | Incorrect webhook format | Verify JSON format and broker requirements |

### Performance Issues
- **Slow execution**: Reduce FVG sensitivity or increase quality threshold
- **Memory usage**: Enable time filtering to reduce active zones
- **Alert delays**: Simplify alert messages and check internet connection

## Strategy Validation

### Verification Checklist
- [ ] Strategy produces same signals as indicator
- [ ] Entry/exit timing matches exactly
- [ ] TP/SL levels are calculated identically
- [ ] Trade count matches indicator
- [ ] Performance metrics are within tolerance

### Quality Assurance
- [ ] Test on multiple symbols and timeframes
- [ ] Validate with different parameter combinations
- [ ] Verify alert functionality
- [ ] Check broker integration compatibility
- [ ] Confirm risk management works correctly

## Support and Updates

### Version Information
- **Current Version**: v1.0 (Development)
- **Last Updated**: 2025-06-19
- **Compatibility**: TradingView Pine Script v5

### Development Status
- **Phase 1**: Project Setup ✅ COMPLETE
- **Phase 2-10**: Development in progress
- **Target Completion**: July 2025

### Contact Information
- **Project Repository**: CRT_strategy/
- **Documentation**: CRT_strategy/docs/
- **Issue Tracking**: CRT_strategy/validation/

---
*This guide will be updated as the strategy development progresses*
*Last Updated: 2025-06-19*
*Version: 1.0*
