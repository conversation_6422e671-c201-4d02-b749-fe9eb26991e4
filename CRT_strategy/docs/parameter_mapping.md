# CRT Indicator to Strategy Parameter Mapping

## Overview
This document maps all parameters from the CRT indicator to the strategy implementation, ensuring 100% identical behavior and configuration options.

## Parameter Groups and Mapping

### 1. General Configuration
| Indicator Parameter | Strategy Parameter | Type | Default | Notes |
|-------------------|------------------|------|---------|-------|
| `higherTF` | `higherTF` | timeframe | "240" | Higher timeframe for bulky candle detection |
| `bulkyCandleATRStr` | `bulkyCandleATRStr` | string | "Big" | HTF candle size: Big(2.1), Normal(1.6), Small(1.3) |
| `entryMode` | `entryMode` | string | "FVGs" | Entry mode: "FVGs" or "Order Blocks" |
| `requireRetracement` | `requireRetracement` | bool | false | Require retracement for entry confirmation |
| `showHTFLines` | ~~removed~~ | bool | true | **EXCLUDED**: Visual element not needed in strategy |

### 2. Fair Value Gaps
| Indicator Parameter | Strategy Parameter | Type | Default | Notes |
|-------------------|------------------|------|---------|-------|
| `fvgSensitivityText` | `fvgSensitivityText` | string | "High" | FVG detection sensitivity |
| `showFVG` | ~~removed~~ | bool | true | **EXCLUDED**: Visual element |
| `swingLength` | `swingLength` | int | 10 | Swing length for order block detection |

### 3. Order Blocks
| Indicator Parameter | Strategy Parameter | Type | Default | Notes |
|-------------------|------------------|------|---------|-------|
| `showOB` | ~~removed~~ | bool | true | **EXCLUDED**: Visual element |
| `swingLength` | `swingLength` | int | 10 | Swing length for OB formations |

### 4. TP / SL Configuration
| Indicator Parameter | Strategy Parameter | Type | Default | Notes |
|-------------------|------------------|------|---------|-------|
| `showTPSL` | `enableTPSL` | bool | true | Enable TP/SL system |
| `tpslMethod` | `tpslMethod` | string | "Dynamic" | "Dynamic" or "Fixed" TP/SL method |
| `riskAmount` | `riskAmount` | string | "High" | Dynamic risk level |
| `customSLATRMult` | `customSLATRMult` | float | 6.5 | Custom ATR multiplier (debug) |
| `tpPercent` | `tpPercent` | float | 0.3 | Fixed TP percentage |
| `slPercent` | `slPercent` | float | 0.4 | Fixed SL percentage |

### 5. Enhanced R:R System
| Indicator Parameter | Strategy Parameter | Type | Default | Notes |
|-------------------|------------------|------|---------|-------|
| `useEnhancedRR` | `useEnhancedRR` | bool | false | Enable enhanced R:R system |
| `minRR` | `minRR` | float | 1.5 | Minimum R:R ratio |
| `maxRR` | `maxRR` | float | 2.5 | Maximum R:R ratio |
| `requireVolumeConfirmation` | `requireVolumeConfirmation` | bool | false | Require volume confirmation |
| `minQualityScore` | `minQualityScore` | float | 1.0 | Minimum quality score for entry |

### 6. Partial Profit Taking System ❌ EXCLUDED
| Indicator Parameter | Strategy Parameter | Status | Reason |
|-------------------|------------------|--------|--------|
| `usePartialTPs` | ~~removed~~ | **EXCLUDED** | User requested removal |
| `partialTP1Percent` | ~~removed~~ | **EXCLUDED** | User requested removal |
| `partialTP2Percent` | ~~removed~~ | **EXCLUDED** | User requested removal |
| `useTrailingStop` | ~~removed~~ | **EXCLUDED** | User requested removal |
| `trailingATRMult` | ~~removed~~ | **EXCLUDED** | User requested removal |

### 7. Time Filtering System
| Indicator Parameter | Strategy Parameter | Type | Default | Notes |
|-------------------|------------------|------|---------|-------|
| `useTimeFiltering` | `useTimeFiltering` | bool | false | Enable time-based filtering |
| `avoidWeekends` | `avoidWeekends` | bool | false | Skip weekend trades |
| `avoidLunchHours` | `avoidLunchHours` | bool | false | Skip lunch hours (12:00-13:00) |
| `avoidEarlyMorning` | `avoidEarlyMorning` | bool | false | Skip early morning (00:00-06:00) |
| `useCustomTimeRange` | `useCustomTimeRange` | bool | false | Use custom trading hours |
| `customTimeStart` | `customTimeStart` | int | 9 | Trading start hour |
| `customTimeEnd` | `customTimeEnd` | int | 17 | Trading end hour |
| `tradeLondonOnly` | `tradeLondonOnly` | bool | false | London session only |
| `tradeNewYorkOnly` | `tradeNewYorkOnly` | bool | false | New York session only |
| `avoidAsianSession` | `avoidAsianSession` | bool | false | Avoid Asian session |
| `avoidMondays` | `avoidMondays` | bool | false | Skip Monday trades |
| `avoidFridays` | `avoidFridays` | bool | false | Skip Friday trades |
| `avoidFirstHour` | `avoidFirstHour` | bool | false | Skip first hour (09:00-10:00) |
| `avoidLastHour` | `avoidLastHour` | bool | false | Skip last hour (16:00-17:00) |

### 8. Visual Enhancements ❌ EXCLUDED
| Indicator Parameter | Strategy Parameter | Status | Reason |
|-------------------|------------------|--------|--------|
| `showWinLossMarkers` | ~~removed~~ | **EXCLUDED** | Visual element not needed |
| `showPositionProjection` | ~~removed~~ | **EXCLUDED** | Visual element not needed |

### 9. Alerts
| Indicator Parameter | Strategy Parameter | Type | Default | Notes |
|-------------------|------------------|------|---------|-------|
| `buyAlertEnabled` | `buyAlertEnabled` | bool | true | Enable buy signal alerts |
| `sellAlertEnabled` | `sellAlertEnabled` | bool | true | Enable sell signal alerts |
| `tpAlertEnabled` | `tpAlertEnabled` | bool | true | Enable TP alerts |
| `slAlertEnabled` | `slAlertEnabled` | bool | true | Enable SL alerts |

### 10. Backtesting Dashboard ❌ EXCLUDED
| Indicator Parameter | Strategy Parameter | Status | Reason |
|-------------------|------------------|--------|--------|
| `backtestDisplayEnabled` | ~~removed~~ | **EXCLUDED** | Strategy has built-in backtesting |
| `backtestingLocation` | ~~removed~~ | **EXCLUDED** | Not applicable to strategy |
| `fillBackgrounds` | ~~removed~~ | **EXCLUDED** | Visual element |
| `screenerColor` | ~~removed~~ | **EXCLUDED** | Visual element |

### 11. Visual Settings ❌ EXCLUDED
| Indicator Parameter | Strategy Parameter | Status | Reason |
|-------------------|------------------|--------|--------|
| `dbgTPSLVersion` | ~~removed~~ | **EXCLUDED** | Visual layout setting |
| `fvgBullColor` | ~~removed~~ | **EXCLUDED** | Visual color setting |
| `fvgBearColor` | ~~removed~~ | **EXCLUDED** | Visual color setting |
| `highColor` | ~~removed~~ | **EXCLUDED** | Visual color setting |
| `lowColor` | ~~removed~~ | **EXCLUDED** | Visual color setting |
| `textColor` | ~~removed~~ | **EXCLUDED** | Visual color setting |

## Strategy-Specific Parameters

### New Strategy Parameters
| Parameter | Type | Default | Purpose |
|-----------|------|---------|---------|
| `initialCapital` | float | 100000 | Strategy initial capital |
| `defaultQty` | float | 100 | Default order quantity (% of equity) |
| `commission` | float | 0.1 | Commission percentage |
| `slippage` | int | 3 | Slippage in ticks |
| `pyramiding` | int | 1 | Maximum number of entries in same direction |
| `closeEntriesRule` | string | "FIFO" | Position closing rule |

## Parameter Validation Rules

### Critical Validations
1. **Timeframe Validation**: `higherTF` must be higher than current timeframe
2. **R:R Validation**: `minRR` must be less than `maxRR`
3. **Quality Score**: `minQualityScore` must be between 0.0 and 5.0
4. **Time Range**: `customTimeStart` must be less than `customTimeEnd`
5. **Percentage Validation**: TP/SL percentages must be positive

### ATR Multiplier Mapping
| Risk Level | ATR Multiplier |
|------------|----------------|
| "Highest" | 10.0 |
| "High" | 8.0 |
| "Normal" | 6.5 |
| "Low" | 5.0 |
| "Lowest" | 3.0 |

### FVG Sensitivity Mapping
| Sensitivity | Multiplier |
|-------------|------------|
| "All" | 100 |
| "Extreme" | 6 |
| "High" | 2 |
| "Normal" | 1.5 |
| "Low" | 1 |

## Implementation Notes

### Parameter Groups in Strategy
- Group parameters logically for better user experience
- Maintain exact same tooltips and descriptions
- Preserve parameter order where possible
- Add strategy-specific parameters in separate group

### Backward Compatibility
- All core indicator parameters preserved
- Default values maintained exactly
- Parameter names unchanged (except excluded ones)
- Validation logic identical

### Migration Checklist
- [ ] All 50+ parameters mapped
- [ ] Excluded parameters documented
- [ ] New strategy parameters defined
- [ ] Validation rules implemented
- [ ] Default values verified
- [ ] Parameter groups organized
- [ ] Tooltips and descriptions copied

---
*Last Updated: 2025-06-19*
*Version: 1.0*
